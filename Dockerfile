ARG JFROG_URL
ARG JFROG_DOCKER_LOCAL_REPO
FROM ${JFROG_URL}/${JFROG_DOCKER_LOCAL_REPO}/jf-cli:v2 as jfrog
ARG JFROG_URL
ARG JFROG_USERNAME
ARG JFROG_PASSWORD
ARG JAR_MAVEN_REPO_URL
ARG JFROG_DOCKER_LOCAL_REPO
ARG BASE_IMAGE
ARG APM_AGENT_URL
ARG JFROG_SERVER_ID
WORKDIR /build
RUN jf c add ${JFROG_SERVER_ID} --basic-auth-only --url  "https://${JFROG_URL}" --user ${JFROG_USERNAME} --password ${JFROG_PASSWORD}
# Download java agent from jfrog
RUN jf rt dl --flat ${APM_AGENT_URL}  opentelemetry-javaagent.jar

# Download app jar form jfrog
RUN jf rt dl --flat --sort-by=created --sort-order=desc --limit=1 "${JAR_MAVEN_REPO_URL}/*.jar" app.jar

FROM ${JFROG_URL}/${JFROG_DOCKER_LOCAL_REPO}/${BASE_IMAGE}

EXPOSE 8080

# RUN echo "Asia/Shanghai" > /etc/timezone
RUN adduser cus-ms-user -D
WORKDIR /home/<USER>
COPY --from=jfrog ./build/*.jar .
RUN chown cus-ms-user:cus-ms-user *
USER cus-ms-user

# JVM PARAMETERS
ENV JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError

# PROFILE ENVIRONMENT
ENV ENV "injected"
ENV APP_NAME "injected"
ENV APM_AGENT_VERSION ${APM_AGENT_VERSION}

# AliCloud Database Configuration
ENV DATABASE_URL "injected"
ENV DATABASE_USERNAME "injected"
ENV DATABASE_PASSWORD "injected"

# Redis
ENV REDIS_URL "injected"
ENV REDIS_PORT "injected"
ENV REDIS_PASSWORD "injected"
ENV REDIS_DATABASE "injected"
ENV REDIS_SSL_CA_CERT_PATH "injected"


# OSS
ENV STORAGE_OSS_ENDPOINT "injected"
ENV STORAGE_OSS_ACCESS_KEY_ID "injected"
ENV STORAGE_OSS_ACCESS_KEY_SECRET "injected"
ENV OSS_BUCKET_ROOT_PATH "injected"
# ENV OSS_BUCKET_BRAND_MAPPING "injected"


# AliCloud Kafka Producer Configuration
ENV KAFKA_TOPIC_NAME "injected"
ENV KAFKA_BOOTSTRAP_SERVERS "injected"
ENV KAFKA_SSL_TRUSTSTORE_LOCATION "injected"
ENV KAFKA_SSL_KEYSTORE_LOCATION "injected"
ENV KAFKA_SSL_TRUSTSTORE_CERT_PATH "injected"
ENV KAFKA_SSL_KEYSTORE_CERT_PATH "injected"
ENV KAFKA_SSL_TRUSTSTORE_PASSWORD "injected"
ENV KAFKA_SSL_KEYSTORE_PASSWORD "injected"
ENV KAFKA_SSL_KEY_PASSWORD "injected"


ENV SECRET_CONFIG_PATH_MARKET_MEMBERTMALL_OKTA "injected"
ENV SECRET_CONFIG_PATH_MARKET_MEMBERTMALL_ISV "injected"

# Open Telemetry endpoint and token
ENV ALICLOUD_OTEL_URL "injected"
ENV ALICLOUD_OTEL_TOKEN "injected"


CMD ["sh", "-c", "java ${JVM_OPTS} \
    -Duser.timezone=\"Asia/Shanghai\" \
    -Duser.language=\"zh\" \
    -Duser.country=\"CN\" \
    -Dspring.profiles.active=${ENV} \
    -javaagent:opentelemetry-javaagent.jar \
    -Dspring.ssl.bundle.pem.redis.truststore.certificate=${REDIS_SSL_CA_CERT_PATH} \
    -Dspring.data.redis.host=${REDIS_URL} \
    -Dspring.data.redis.port=${REDIS_PORT} \
    -Dspring.data.redis.database=${REDIS_DATABASE} \
    -Dspring.data.redis.password=${REDIS_PASSWORD} \
    -Dspring.data.redis.ssl.enabled=true \
    -Dspring.data.redis.ssl.bundle=redis \
    -Dotel.exporter.otlp.protocol=http/protobuf \
    -Dotel.resource.attributes=service.name=${APP_NAME},deployment.environment=${ENV} \
    -Dotel.exporter.otlp.traces.endpoint=${ALICLOUD_OTEL_URL}/adapt_${ALICLOUD_OTEL_TOKEN}/api/otlp/traces \
    -Dotel.logs.exporter=none \
    -Dotel.metrics.exporter=none \
    -Dotel.trace.exporter=otlp \
    -Dotel.javaagent.logging=application \
    -jar app.jar"]
