package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.service.MemberService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InitMemberActionServiceImplTest {
    @Mock
    private BizMemberMapper bizMemberMapper;

    @InjectMocks
    private InitMemberActionServiceImpl initMemberActionService;

    @Test
    void testExecuteSuccess() {
        String body = "{\"bizRequest\":\"{\\\"ouid\\\":\\\"AAErAbHTAADTo9wAAe6E5rZH\\\"," +
                "\\\"mixMobile\\\":\\\"xxx\\\",\\\"nick\\\":\\\"我命由我不由天\\\",\\\"gender\\\":" +
                "\\\"1\\\",\\\"province\\\":\\\"上海\\\",\\\"city\\\":\\\"黄浦区\\\",\\\"" +
                "subscribeTime\\\":\\\"2025-07-01 12:00:00\\\"}\"}";
        // 模拟初始化成功场景
        JSONObject result =
                initMemberActionService.execute(body);
        assertNull(result);
        verify(bizMemberMapper).insert(any());
    }
}