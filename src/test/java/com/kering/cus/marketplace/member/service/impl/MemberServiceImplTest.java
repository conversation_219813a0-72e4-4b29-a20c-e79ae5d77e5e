package com.kering.cus.marketplace.member.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.marketplace.member.config.ECStoreProperties;
import com.kering.cus.marketplace.member.enums.ResultCode;
import com.kering.cus.marketplace.member.enums.ThirdPartyStatusEnum;
import com.kering.cus.marketplace.member.exception.BizException;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.SecurityDto;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.model.param.MemberQueryPageParam;
import com.kering.cus.marketplace.member.model.result.MemberPageResult;
import com.kering.cus.marketplace.member.model.result.MemberResult;
import com.kering.cus.marketplace.member.util.AliyunOssUtil;
import com.kering.cus.marketplace.member.util.SecurityUtils;
import com.kering.cus.marketplace.member.util.ThirdPartyTaskUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.scheduling.annotation.Async;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class MemberServiceImplTest {
    private static final int MAX_PAGE_SIZE = 100;
    @InjectMocks
    private MemberServiceImpl memberService;

    @Mock
    private BizMemberMapper bizMemberMapper;

    @Mock
    private AliyunOssUtil aliyunOssUtil;

    @Mock
    private ECStoreProperties eCStoreProperties;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ========== 公共接口方法测试 ==========
    @Nested
    @DisplayName("Public Interface Tests")
    class PublicInterfaceTests {
        @Test
        @DisplayName("分页查询-成功")
        void getMemberPageList_Success() {
            MemberQueryPageParam param = new MemberQueryPageParam();
            param.setPageNum(1);
            param.setPageSize(10);
            param.setOuid("testOuid");
            param.setNick("testNick");
            param.setMobile("13812345678");
            param.setClientId("testClient");
            param.setSubscribeStartTime("2024-01-24T16:00:00.000Z");
            param.setSubscribeEndTime("2023-12-31T23:59:59");

            Page<BizMember> mockPage = new Page<>();
            mockPage.setRecords(new ArrayList<>());
            when(bizMemberMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            IPage<MemberPageResult> result = memberService.getMemberPageList(param);

            assertNotNull(result);
            verify(bizMemberMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("分页查询-参数为空")
        void getMemberPageList_NullParam() {
            IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                    () -> memberService.getMemberPageList(null));
            assertEquals("参数不能为空", exception.getMessage());
        }

        @Test
        @DisplayName("会员详情-成功")
        void getMemberDetail_Success() {
            String memberId = "123";
            BizMember mockMember = new BizMember();
            mockMember.setId(memberId);
            mockMember.setMobile("13812345678");
            mockMember.setCreatedDate(DateUtil.date());
            when(bizMemberMapper.selectById(memberId)).thenReturn(mockMember);

            MemberResult result = memberService.getMemberDetail(memberId);

            assertNotNull(result);
            assertEquals(memberId, result.getId());
            assertEquals("138****5678", result.getMobile());
        }

        @Test
        @DisplayName("会员详情-未找到")
        void getMemberDetail_NotFound() {
            String memberId = "123";
            when(bizMemberMapper.selectById(memberId)).thenReturn(null);

            BizException exception = assertThrows(BizException.class,
                    () -> memberService.getMemberDetail(memberId));
            assertEquals(ResultCode.MEMBER_IS_EMPTY, exception.getResultCode());
        }

        @Test
        @DisplayName("会员详情-空ID")
        void getMemberDetail_EmptyId() {
            BizException exception = assertThrows(BizException.class,
                    () -> memberService.getMemberDetail(null));
            assertEquals(ResultCode.MEMBER_ID_IS_EMPTY, exception.getResultCode());
        }

        @Test
        @DisplayName("原始手机号-成功")
        void getOriginalMobile_Success() {
            String memberId = "123";
            BizMember mockMember = new BizMember();
            mockMember.setId(memberId);
            mockMember.setMobile("13812345678");
            when(bizMemberMapper.selectById(memberId)).thenReturn(mockMember);

            String mobile = memberService.getOriginalMobile(memberId);

            assertEquals("13812345678", mobile);
        }

        @Test
        @DisplayName("原始手机号-会员不存在")
        void getOriginalMobile_MemberNotFound() {
            String memberId = "123";
            when(bizMemberMapper.selectById(memberId)).thenReturn(null);

            BizException exception = assertThrows(BizException.class,
                    () -> memberService.getOriginalMobile(memberId));
            assertEquals(ResultCode.MEMBER_IS_EMPTY, exception.getResultCode());
        }

        @Test
        @DisplayName("原始手机号-空ID")
        void getOriginalMobile_EmptyId() {
            BizException exception = assertThrows(BizException.class,
                    () -> memberService.getOriginalMobile(null));
            assertEquals(ResultCode.MEMBER_ID_IS_EMPTY, exception.getResultCode());
        }
    }
    @Test
    @Async
    @DisplayName("导出任务-成功(空数据)")
    void export_Success_EmptyData() {
        MemberQueryPageParam param = new MemberQueryPageParam();
        String taskId = "task123";
        SecurityDto securityDto = new SecurityDto();
        Page<BizMember> mockPage = new Page<>();
        mockPage.setRecords(new ArrayList<>());
        when(bizMemberMapper.selectPage(any(), any())).thenReturn(mockPage);
        when(aliyunOssUtil.uploadFile(any(), any(), any(), any()))
                .thenReturn("http://oss.example.com/file.zip");
        try (MockedStatic<ThirdPartyTaskUtil> mockedStatic = mockStatic(ThirdPartyTaskUtil.class)) {
            mockedStatic.when(() -> ThirdPartyTaskUtil.updateTask(any(), any(), any())).thenCallRealMethod();
            memberService.export(param, taskId, securityDto);
            verify(bizMemberMapper, times(1)).selectPage(any(), any());
        }
    }
    @Test
    @Async
    @DisplayName("导出任务-成功(有数据)")
    void export_Success_WithData() {
        MemberQueryPageParam param = new MemberQueryPageParam();
        param.setPageSize(MAX_PAGE_SIZE);
        String taskId = "task123";
        SecurityDto securityDto = new SecurityDto();

        // 准备测试数据 - 第一页有数据，第二页空数据
        List<BizMember> firstPageRecords = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            BizMember member = new BizMember();
            member.setId("id" + i);
            member.setMobile("1381234567" + i);
            member.setNick("testNick" + i);
            member.setOuid("ouid" + i);
            member.setClientId("client" + i);
            firstPageRecords.add(member);
        }

        Page<BizMember> firstPage = new Page<>();
        firstPage.setRecords(firstPageRecords);

        Page<BizMember> emptyPage = new Page<>();
        emptyPage.setRecords(new ArrayList<>());

        // 模拟分页查询 - 第一次返回有数据，第二次返回空
        when(bizMemberMapper.selectPage(any(), any()))
            .thenReturn(firstPage)
            .thenReturn(firstPage)
            .thenReturn(emptyPage);

        // 模拟文件操作
        File mockFile = mock(File.class);
        when(mockFile.getName()).thenReturn("member_data_export_12345678.zip");
        when(aliyunOssUtil.uploadFile(any(), any(), any(), any()))
                .thenReturn("http://oss.example.com/file.zip");
        try (MockedStatic<ThirdPartyTaskUtil> mockedStatic = mockStatic(ThirdPartyTaskUtil.class)) {
            mockedStatic.when(()->ThirdPartyTaskUtil.updateTask(any(),any(),any())).thenCallRealMethod();
            memberService.export(param, taskId, securityDto);
            // 验证数据库调用 - 应该调用两次
            verify(bizMemberMapper, times(3)).selectPage(any(), any());
        }

    }

    @Test
    @DisplayName("导出任务-OSS上传失败")
    void export_OssUploadFailure() {
        MemberQueryPageParam param = new MemberQueryPageParam();
        String taskId = "task123";
        SecurityDto securityDto = new SecurityDto();
        Page<BizMember> mockPage = new Page<>();
        mockPage.setRecords(new ArrayList<>());
        when(bizMemberMapper.selectPage(any(), any())).thenReturn(mockPage);
        when(aliyunOssUtil.uploadFile(any(), any(), any(), any()))
                .thenThrow(new RuntimeException("OSS error"));
        try (MockedStatic<ThirdPartyTaskUtil> mockedStatic = mockStatic(ThirdPartyTaskUtil.class)) {
            mockedStatic.when(() -> ThirdPartyTaskUtil.updateTask(any(), any(), any())).thenCallRealMethod();
            memberService.export(param, taskId, securityDto);
            mockedStatic.verify(()->ThirdPartyTaskUtil.updateTask(eq(taskId),
                    eq(ThirdPartyStatusEnum.ERROR), contains("导出失败")));
        }
    }

    @Test
    void testUpdateMemberByCDB_MemberExists() {
        // Arrange
        CBDUserDTO cbdUserDTO = new CBDUserDTO();
        cbdUserDTO.setGender("M");
        cbdUserDTO.setClientId("test-client-id");
        CBDUserDTO.Activity activity = new CBDUserDTO.Activity();
        activity.setCustSinceDt(LocalDateTime.now());
        activity.setRegistrStore("123");
        cbdUserDTO.setActivity(activity);

        ECStoreProperties.Tenant tenant = new ECStoreProperties.Tenant();
        tenant.setTenantId("1");
        tenant.setStoreCodes(List.of("123"));
        when(eCStoreProperties.getList()).thenReturn(List.of(tenant));
        BizMember existingMember = new BizMember();
        existingMember.setId("1L");
        existingMember.setOuid("test-ou-id");
        when(bizMemberMapper.selectOne(any())).thenReturn(existingMember);
        try (MockedStatic<SecurityUtils> mockedStatic = mockStatic(SecurityUtils.class)) {
            mockedStatic.when(SecurityUtils::getTenantId).thenReturn("1");
            memberService.updateMemberByCDB(cbdUserDTO);
            // Assert
            verify(bizMemberMapper, times(1)).updateById(any(BizMember.class));
        }
    }

    @Test
    void testUpdateMemberByCDB_MemberExistsAndStoreTypeOthers() {
        // Arrange
        CBDUserDTO cbdUserDTO = new CBDUserDTO();
        cbdUserDTO.setGender("M");
        cbdUserDTO.setClientId("test-client-id");
        CBDUserDTO.Activity activity = new CBDUserDTO.Activity();
        activity.setCustSinceDt(LocalDateTime.now());
        activity.setRegistrStore("123");
        cbdUserDTO.setActivity(activity);

        ECStoreProperties.Tenant tenant = new ECStoreProperties.Tenant();
        tenant.setTenantId("1");
        tenant.setStoreCodes(List.of("111"));
        when(eCStoreProperties.getList()).thenReturn(List.of(tenant));
        BizMember existingMember = new BizMember();
        existingMember.setId("1L");
        existingMember.setOuid("test-ou-id");
        when(bizMemberMapper.selectOne(any())).thenReturn(existingMember);
        try (MockedStatic<SecurityUtils> mockedStatic = mockStatic(SecurityUtils.class)) {
            mockedStatic.when(SecurityUtils::getTenantId).thenReturn("1");
            memberService.updateMemberByCDB(cbdUserDTO);
            // Assert
            verify(bizMemberMapper, times(1)).updateById(any(BizMember.class));
        }
    }

    @Test
    void testUpdateMemberByCDB_NoActive() {
        // Arrange
        CBDUserDTO cbdUserDTO = new CBDUserDTO();
        cbdUserDTO.setGender("M");
        cbdUserDTO.setClientId("test-client-id");

        BizMember existingMember = new BizMember();
        existingMember.setId("1L");
        existingMember.setOuid("test-ou-id");
        when(bizMemberMapper.selectOne(any())).thenReturn(existingMember);
        memberService.updateMemberByCDB(cbdUserDTO);
        // Assert
        verify(bizMemberMapper, times(1)).updateById(any(BizMember.class));
    }

    @Test
    void testUpdateMemberByCDB_NoStore() {
        // Arrange
        CBDUserDTO cbdUserDTO = new CBDUserDTO();
        cbdUserDTO.setGender("M");
        cbdUserDTO.setClientId("test-client-id");
        CBDUserDTO.Activity activity = new CBDUserDTO.Activity();
        activity.setCustSinceDt(LocalDateTime.now());
        cbdUserDTO.setActivity(activity);

        BizMember existingMember = new BizMember();
        existingMember.setId("1L");
        existingMember.setOuid("test-ou-id");
        when(bizMemberMapper.selectOne(any())).thenReturn(existingMember);
        memberService.updateMemberByCDB(cbdUserDTO);
        // Assert
        verify(bizMemberMapper, times(1)).updateById(any(BizMember.class));
    }

    @Test
    void testUpdateMemberByCDB_NoTenants() {
        CBDUserDTO cbdUserDTO = new CBDUserDTO();
        cbdUserDTO.setGender("M");
        cbdUserDTO.setClientId("test-client-id");
        CBDUserDTO.Activity activity = new CBDUserDTO.Activity();
        activity.setCustSinceDt(LocalDateTime.now());
        activity.setRegistrStore("123");
        cbdUserDTO.setActivity(activity);

        when(eCStoreProperties.getList()).thenReturn(null);
        BizMember existingMember = new BizMember();
        existingMember.setId("1L");
        existingMember.setOuid("test-ou-id");
        when(bizMemberMapper.selectOne(any())).thenReturn(existingMember);
        memberService.updateMemberByCDB(cbdUserDTO);
        // Assert
        verify(bizMemberMapper, times(1)).updateById(any(BizMember.class));
    }


    @Test
    void testUpdateMemberByCDB_MemberDoesNotExist() {
        // Arrange
        CBDUserDTO cbdUserDTO = new CBDUserDTO();
        when(bizMemberMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        // Act
        memberService.updateMemberByCDB(cbdUserDTO);
        // Assert
        verify(bizMemberMapper, never()).updateById(any(BizMember.class));
    }


}