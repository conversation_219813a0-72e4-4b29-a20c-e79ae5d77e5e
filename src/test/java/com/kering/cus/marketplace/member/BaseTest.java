package com.kering.cus.marketplace.member;

import org.junit.jupiter.api.BeforeAll;
import org.springframework.test.context.ActiveProfiles;

/**
 * 测试基类，统一处理测试环境配置
 */
@ActiveProfiles("test")
public abstract class BaseTest {
    
    @BeforeAll
    static void setupTestEnvironment() {
        // 设置无头模式，避免字体配置问题
        System.setProperty("java.awt.headless", "true");
        
        // 设置字体相关属性
        System.setProperty("awt.useSystemAAFontSettings", "on");
        System.setProperty("swing.aatext", "true");
        
        // 禁用字体缓存，避免字体配置问题
        System.setProperty("sun.java2d.fontpath", "");
        
        // 设置POI相关属性，避免Excel操作时的字体问题
        System.setProperty("org.apache.poi.util.POILogger", "org.apache.poi.util.NullLogger");
    }
}
