package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.model.result.Result;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AnonymousMemberActionServiceImplTest {
    @InjectMocks
    private AnonymousMemberActionServiceImpl anonymousMemberActionService;
    @Mock
    private BizMemberMapper bizMemberMapper;

    @Test
    void testExecute() {
        String body = "{\"bizRequest\":\"{\\\"ouid\\\":\\\"AAErAbHTAADTo9wAAe6E5rZH\\\",\\\"mixMobile\\\":\\\"xxx\\\",\\\"nick\\\":\\\"我命由我不由天\\\",\\\"gender\\\":\\\"1\\\",\\\"province\\\":\\\"上海\\\",\\\"city\\\":\\\"黄浦区\\\",\\\"subscribeTime\\\":\\\"2025-07-01 12:00:00\\\"}\"}\n";
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(),""), BizMember.class);
        JSONObject result = anonymousMemberActionService.execute(body);
        assertNull(result);
    }
}