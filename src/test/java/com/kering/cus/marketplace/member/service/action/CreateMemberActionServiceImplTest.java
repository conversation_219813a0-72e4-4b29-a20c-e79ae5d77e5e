package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.model.result.Result;
import com.kering.cus.marketplace.member.util.CBDUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateMemberActionServiceImplTest {
    @InjectMocks
    private CreateMemberActionServiceImpl createMemberActionService;

    @Mock
    private BizMemberMapper bizMemberMapper;


    @Test
    void testExecuteWithValidInput() {
        String validInput = "{\"bizRequest\":\"{\\\"query\\\":\\\"\\\",\\\"variables\\\":{\\\"data\\\":{\\\"client_id\\\":\\\"123\\\"}}}\"}";
        try (MockedStatic<CBDUtils> mockedStatic = mockStatic(CBDUtils.class)) {
            JSONObject mockResponse = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("clientId", "test123");
            data.put("createCustomer", jsonObject);
            mockResponse.put("data", data);

            mockedStatic.when(() -> CBDUtils.mutation(any())).thenReturn(mockResponse);
            when(bizMemberMapper.exists(any())).thenReturn(false);
            JSONObject result = createMemberActionService.execute(validInput);
            assertNotNull(result);
            assertTrue(result.containsKey("data"));
            assertEquals("test123", result.getJSONObject("data")
                                       .getJSONObject("createCustomer")
                                       .getString("clientId"));
        }
    }

    @Test
    void testExecuteWithValidInput_update() {
        String validInput = "{\"bizRequest\":\"{\\\"query\\\":\\\"\\\",\\\"variables\\\":{\\\"data\\\":{\\\"client_id\\\":\\\"123\\\"}}}\"}";
        try (MockedStatic<CBDUtils> mockedStatic = mockStatic(CBDUtils.class)) {
            JSONObject mockResponse = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("clientId", "test123");
            data.put("createCustomer", jsonObject);
            mockResponse.put("data", data);

            mockedStatic.when(() -> CBDUtils.mutation(any())).thenReturn(mockResponse);
            when(bizMemberMapper.exists(any())).thenReturn(true);
            when(bizMemberMapper.selectOne(any())).thenReturn(new BizMember());
            JSONObject result = createMemberActionService.execute(validInput);
            assertNotNull(result);
            assertTrue(result.containsKey("data"));
            assertEquals("test123", result.getJSONObject("data")
                    .getJSONObject("createCustomer")
                    .getString("clientId"));
        }
    }


    @Test
    void testExecuteWithInvalidJson() {
        String invalidInput = "invalid json";
        assertThrows(Exception.class, () -> createMemberActionService.execute(invalidInput));
    }
}