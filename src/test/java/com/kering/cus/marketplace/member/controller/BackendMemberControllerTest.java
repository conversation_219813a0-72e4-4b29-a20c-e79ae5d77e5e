package com.kering.cus.marketplace.member.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dtflys.forest.http.HttpStatus;
import com.kering.cus.marketplace.member.enums.OssBucketType;
import com.kering.cus.marketplace.member.enums.ResultCode;
import com.kering.cus.marketplace.member.model.param.MemberQueryPageParam;
import com.kering.cus.marketplace.member.model.result.MemberResult;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.util.AliyunOssUtil;
import com.kering.cus.marketplace.member.util.ThirdPartyTaskUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.servlet.handler.SimpleMappingExceptionResolver;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Properties;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyByte;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class BackendMemberControllerTest {
    private MockMvc mockMvc;
    @InjectMocks
    private BackendMemberController backendMemberController;
    @Mock
    private MemberService memberService;

    @Mock
    private AliyunOssUtil aliyunOssUtil;

    @BeforeEach
    void setUp() throws Exception {
        mockMvc = MockMvcBuilders.standaloneSetup(backendMemberController)
                .setHandlerExceptionResolvers(getSimpleExceptionResolver())
                .build();
    }
    private SimpleMappingExceptionResolver getSimpleExceptionResolver() {
        SimpleMappingExceptionResolver result
                = new SimpleMappingExceptionResolver();

        // Setting customized exception mappings
        Properties p = new Properties();
        p.put(SimpleMappingExceptionResolver.class.getName(), "Errors/Exception");
        result.setExceptionMappings(p);

        // Unmapped exceptions will be directed there
        result.setDefaultErrorView("Errors/Default");

        // Setting a default HTTP status code
        result.setDefaultStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);

        return result;
    }



    @Test
    void testGetMemberPageList() throws Exception {
        MemberQueryPageParam param = new MemberQueryPageParam();
        Mockito.when(memberService.getMemberPageList(param))
               .thenReturn(Page.of(0,0));
        mockMvc.perform(get("/backend/member")
               .param("pageNum", "1")
               .param("pageSize", "10"))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
    }

    @Test
    void testGetMemberDetail() throws Exception {
        String memberId = "test123";
        MemberResult expectedResult = new MemberResult();

        Mockito.when(memberService.getMemberDetail(memberId))
               .thenReturn(expectedResult);

        mockMvc.perform(get("/backend/member/{id}", memberId))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
    }

    @Test
    void testGetOriginalMobile() throws Exception {
        String memberId = "test123";
        String mobile = "13800138000";

        Mockito.when(memberService.getOriginalMobile(memberId))
                .thenReturn(mobile);
        mockMvc.perform(get("/backend/member/original/mobile/{id}", memberId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
    }
    @Test
    public void testExport() throws Exception {
        mockMvc.perform(get("/backend/member/export")
               .param("pageNum", "1")
               .param("pageSize", "10"))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.code").value(ResultCode.SUCCESS.getCode()));
    }

    @Test
    public void testExportWithTaskCreationFailure() throws Exception {
        try (MockedStatic<ThirdPartyTaskUtil> utilMock = Mockito.mockStatic(ThirdPartyTaskUtil.class)) {
            utilMock.when(() -> ThirdPartyTaskUtil.createTask(any(), any(), any(), any()))
                   .thenThrow(new RuntimeException("Task creation failed"));

            mockMvc.perform(get("/backend/member/export")
                   .param("pageNum", "1")
                   .param("pageSize", "10"))
                   .andExpect(status().is5xxServerError());
        }
    }

    @Test
    public void testDownloadThirdPartySuccess() throws Exception {
        String testUrl = "testUrl";
        byte[] testBytes = "testContent".getBytes();

        Mockito.when(aliyunOssUtil.getFileBytes(testUrl, OssBucketType.PRIVATE))
               .thenReturn(testBytes);

        mockMvc.perform(get("/backend/member/download/third-party")
               .param("url", testUrl))
               .andExpect(status().isOk())
               .andExpect(header().string("Content-Disposition", containsString(".ZIP")));
    }

    @Test
    public void testGetMemberDetailNotFound() throws Exception {
        String memberId = "notExistId";

        Mockito.when(memberService.getMemberDetail(memberId))
               .thenReturn(null);

        mockMvc.perform(get("/backend/member/{id}", memberId))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.data").doesNotExist());
    }
    @Test
    public void testGetOriginalMobileNotFound() throws Exception {
        String memberId = "notExistId";

        Mockito.when(memberService.getOriginalMobile(memberId))
               .thenReturn(null);

        mockMvc.perform(get("/backend/member/original/mobile/{id}", memberId))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.data").doesNotExist());
    }

    @Test
    void downloadThirdPartyTest() throws Exception{
        when(aliyunOssUtil.getFileBytes(any(),any())).thenReturn(new byte[]{});
        mockMvc.perform(get("/backend/member/download/third-party")
                        .param("url","1"))
                .andExpect(status().isOk());
    }
}