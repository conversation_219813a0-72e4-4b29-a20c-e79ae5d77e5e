package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.model.result.MemberResult;
import com.kering.cus.marketplace.member.model.result.Result;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.util.CBDUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GetMemberActionServiceImplTest {
    @Mock
    private MemberService memberService;

    @InjectMocks
    private GetMemberActionServiceImpl getMemberActionService;

    @Test
    void testExecuteWithExistingMember() {
        String body = "{\"bizRequest\":\"{\\\"query\\\":\\\"query " +
                "GetCustomersBySocial($brand:Brand!,$social:getCustomersBySocialInput!," +
                "$partialChannelName:Boolean,$options:SearchOptionsInput){getCustomersBySocial" +
                "(brand:$brand,social:$social,partialChannelName:$partialChannelName,options:$options)" +
                "{customers{client_id brand gender address_primary_country address{name type current primary " +
                "line_1 line_2 po_box district zip_code city state province country}" +
                "birth{yyyy dd_mm certified_yyyy certified_ddmm lunar}email{primary medium_type address}" +
                "name{first{local}last{local}per_title}phone{mobile{pref num val_flg}}social{name channel user_id " +
                "client_nickname recruitment_channel language follow_flag verification_flag last_subscription_date " +
                "last_unsubscription_date}update_date}}}\\\",\\\"variables\\\":{\\\"social\\\":{\\\"id\\\":\\\"" +
                "AAErAbHTAADTo9wAAe6E5rZH\\\",\\\"channel\\\":\\\"Tmall\\\"},\\\"brand\\\":\\\"BV\\\"}}\"}";
        try (MockedStatic<CBDUtils> cbdUtilsMockedStatic = mockStatic(CBDUtils.class)) {
            JSONObject mockResponse = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("clientId", "test123");
            data.put("customers", jsonObject);
            mockResponse.put("data", data);
            cbdUtilsMockedStatic.when(()->CBDUtils.query(any())).thenReturn(mockResponse);
            JSONObject result = getMemberActionService.execute(body);
            assertNotNull(result);
        }
    }
}