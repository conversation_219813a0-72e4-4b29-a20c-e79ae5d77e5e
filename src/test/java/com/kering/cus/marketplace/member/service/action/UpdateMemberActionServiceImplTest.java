package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.util.CBDUtils;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateMemberActionServiceImplTest {
    @Mock
    private BizMemberMapper bizMemberMapper;
    @InjectMocks
    private UpdateMemberActionServiceImpl updateMemberActionService;

    @Test
    void testExecuteSuccess() {
        String validInput = "{\n" +
                "    \"bizRequest\":\"{\\\"variables\\\":{\\\"data\\\":{\\\"activity\\\":{\\\"cust_since_dt\\\":\\\"2022-08-05T11:54:09.000\\\",\\\"last_upd_dt\\\":\\\"2025-08-05T16:10:55.018\\\",\\\"registr_store\\\":\\\"62176\\\"},\\\"address\\\":[{\\\"city\\\":\\\"\\\",\\\"country\\\":\\\"CHINESE_MAINLAND\\\",\\\"current\\\":true,\\\"district\\\":\\\"\\\",\\\"line_1\\\":\\\"\\\",\\\"line_2\\\":\\\"\\\",\\\"primary\\\":true,\\\"province\\\":\\\"_EMPTY\\\",\\\"state\\\":\\\"_EMPTY\\\",\\\"type\\\":\\\"_EMPTY\\\"}],\\\"address_primary_country\\\":\\\"CHINESE_MAINLAND\\\",\\\"contact\\\":{\\\"flg\\\":{\\\"email\\\":true,\\\"mail\\\":true,\\\"phone\\\":true,\\\"sms\\\":true}},\\\"gender\\\":\\\"PREFER_NOT_SAY\\\",\\\"name\\\":{\\\"first\\\":{\\\"local\\\":\\\"N/A\\\"},\\\"last\\\":{\\\"local\\\":\\\"N/A\\\"},\\\"per_title\\\":\\\"PREFER_NOT_SAY\\\"},\\\"phone\\\":{\\\"mobile\\\":{\\\"num\\\":\\\"18221100165\\\",\\\"pref\\\":\\\"_86\\\",\\\"val_flg\\\":true}},\\\"social\\\":[{\\\"channel\\\":\\\"Tmall\\\",\\\"client_nickname\\\":\\\"梦见仙女7\\\",\\\"follow_flag\\\":true,\\\"language\\\":\\\"SIMPLIFIED_CHINESE\\\",\\\"last_subscription_date\\\":\\\"2022-08-05T11:54:09.000\\\",\\\"recruitment_channel\\\":\\\"TMALL\\\",\\\"user_id\\\":\\\"AAHRAbHTAADTo94AAe3ggh2N\\\",\\\"verification_flag\\\":true}],\\\"typology\\\":{\\\"flg\\\":{\\\"outside_data\\\":true,\\\"prtn_share\\\":false,\\\"prv_mark\\\":true,\\\"prv_prof\\\":true},\\\"prsp_src\\\":\\\"TMALL\\\"}},\\\"brand\\\":\\\"BV\\\",\\\"storeCode\\\":\\\"62176\\\"},\\\"query\\\":\\\"mutation createCustomer($brand:Brand!,$storeCode:String!,$data:CreationCustomerInput!){createCustomer(brand:$brand,storeCode:$storeCode,data:$data){__typename...on MutationSuccess{clientId transactionId jwt}...on MutationError{transactionId errorCode errorMessage\\\"}\"\n" +
                "}";
        try (MockedStatic<CBDUtils> mockedStatic = mockStatic(CBDUtils.class)) {
            JSONObject mockResponse = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("clientId", "test123");
            data.put("updateCustomer", jsonObject);
            mockResponse.put("data", data);
            TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(),""), BizMember.class);
            when(bizMemberMapper.selectOne(any())).thenReturn(new BizMember());
            mockedStatic.when(() -> CBDUtils.mutation(any())).thenReturn(mockResponse);
            JSONObject result = updateMemberActionService.execute(validInput);
            assertNotNull(result);
            assertTrue(result.containsKey("data"));
        }
    }

    @Test
    void testExecuteWithException() {
        String invalidInput = "invalid json";
        assertThrows(Exception.class, () -> updateMemberActionService.execute(invalidInput));
    }
}
