package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.util.CBDUtils;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateMemberActionServiceImplTest {
    @Mock
    private BizMemberMapper bizMemberMapper;
    @InjectMocks
    private UpdateMemberActionServiceImpl updateMemberActionService;

    @Test
    void testExecuteSuccess() {
        String validInput = "{\"bizRequest\":\"{\\\"query\\\":\\\"\\\",\\\"variables\\\":{\\\"data\\\":{\\\"client_id\\\":\\\"123\\\"}}}\"}";
        try (MockedStatic<CBDUtils> mockedStatic = mockStatic(CBDUtils.class)) {
            JSONObject mockResponse = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("clientId", "test123");
            data.put("updateCustomer", jsonObject);
            mockResponse.put("data", data);
            TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(),""), BizMember.class);
            when(bizMemberMapper.selectOne(any())).thenReturn(new BizMember());
            mockedStatic.when(() -> CBDUtils.mutation(any())).thenReturn(mockResponse);
            JSONObject result = updateMemberActionService.execute(validInput);
            assertNotNull(result);
            assertTrue(result.containsKey("data"));
        }
    }

    @Test
    void testExecuteWithException() {
        String invalidInput = "invalid json";
        assertThrows(Exception.class, () -> updateMemberActionService.execute(invalidInput));
    }
}
