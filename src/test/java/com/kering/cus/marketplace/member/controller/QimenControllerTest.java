package com.kering.cus.marketplace.member.controller;

import com.alibaba.fastjson2.JSON;
import com.kering.cus.marketplace.member.service.QimenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
@ExtendWith(MockitoExtension.class)
class QimenControllerTest {

    private MockMvc mockMvc;

    @InjectMocks
    private QimenController qimenController;

    @Mock
    private QimenService qimenService;

    @BeforeEach
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.standaloneSetup(qimenController).build();
    }


    @Test
    public void testCallbackSuccess() throws Exception {
        String requestBody = "{\"test\":\"data\"}";
        String brand = "testBrand";
        String action = "testAction";
        System.out.println(JSON.toJSONString(null).length());
        Mockito.when(qimenService.checkSign(Mockito.any(), Mockito.any()))
               .thenReturn(true);
        mockMvc.perform(MockMvcRequestBuilders.post("/qimen/callback")
               .param("brand", brand)
               .param("action", action)
               .content(requestBody))
               .andExpect(status().isOk());
    }

    @Test
    public void testCallbackSignFailure() throws Exception {
        String requestBody = "{\"test\":\"data\"}";
        String brand = "testBrand";
        String action = "testAction";

        Mockito.when(qimenService.checkSign(Mockito.any(), Mockito.any()))
               .thenReturn(false);

        mockMvc.perform(MockMvcRequestBuilders.post("/qimen/callback")
               .param("brand", brand)
               .param("action", action)
               .content(requestBody))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$.flag").value("failure"))
               .andExpect(jsonPath("$.sub_code").value("sign-check-filure"));
    }
}