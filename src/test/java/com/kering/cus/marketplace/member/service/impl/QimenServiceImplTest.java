package com.kering.cus.marketplace.member.service.impl;

import com.kering.cus.marketplace.member.constants.Constants;
import com.kering.cus.marketplace.member.factory.ActionExecuteFactoty;
import com.kering.cus.marketplace.member.service.ActionService;
import com.kering.cus.marketplace.member.util.SignUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class QimenServiceImplTest {
    @Mock
    private ActionExecuteFactoty actionExecuteFactoty;
    @Mock
    private HttpServletRequest request;
    @InjectMocks
    private QimenServiceImpl service;

    @Test
    void testCheckSignSuccess() throws IOException {
        // 准备测试数据
        String body = "testBody";
        Map<String, String> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", "value2");

        // 模拟签名
        String expectedSign = SignUtil.signTopRequest(params, body, null, Constants.SIGN_METHOD_MD5);

        // 模拟request行为
        when(request.getQueryString()).thenReturn("key1=value1&key2=value2&sign=" + expectedSign);
        when(request.getHeader("top-sign-list")).thenReturn(null);

        // 执行测试
        boolean result = service.checkSign(request, body);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testCheckSignFailure() throws IOException {
        // 准备测试数据
        String body = "testBody";

        // 模拟request行为
        when(request.getQueryString()).thenReturn("key1=value1&key2=value2&sign=wrongSign");
        when(request.getHeader("top-sign-list")).thenReturn(null);

        // 执行测试
        boolean result = service.checkSign(request, body);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testExecute() {
        String action = "testAction";
        String body = "testBody";
        ActionService mockAction = mock(ActionService.class);

        when(actionExecuteFactoty.getAction(action)).thenReturn(mockAction);

        String result = service.execute(body, action);

        verify(mockAction).execute(body);
        assertNotNull(result); // 根据实现类，当前返回null
    }

    @Test
    void testGetHeaderMap() throws IOException {
        // 测试getHeaderMap方法
        when(request.getHeader("top-sign-list")).thenReturn("header1,header2");
        when(request.getHeader("header1")).thenReturn("value1");
        when(request.getHeader("header2")).thenReturn(null);

        Map<String, String> result = service.getHeaderMap(request, "UTF-8");

        assertEquals(2, result.size());
        assertEquals("value1", result.get("header1"));
        assertEquals("", result.get("header2"));
    }

    @Test
    void testGetQueryMap() throws IOException {
        // 测试getQueryMap方法
        when(request.getQueryString()).thenReturn("key1=value1&key2=&key3=value3");

        Map<String, String> result = service.getQueryMap(request, "UTF-8");

        assertEquals(3, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("", result.get("key2"));
        assertEquals("value3", result.get("key3"));
    }
}