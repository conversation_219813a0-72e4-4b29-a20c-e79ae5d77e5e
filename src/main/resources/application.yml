spring:
  main:
    allow-circular-references: true
  data:
    redis:
      database: ${REDIS_DATABASE:0}
      host: ${REDIS_URL}
      port: ${REDIS_POST:6379}
      password: ${REDIS_PASSWORD}
      lettuce:
        pool:
          min-idle: 1
  cache:
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 3600000
      # 缓存null值，防止缓存穿透
      cache-null-values: true
  kafka:
    #    ssl:
    #      key-password: ${KAFKA_SSL_KEY_PASSWORD}
    #      trust-store-location: ${KAFKA_SSL_TRUSTSTORE_LOCATION}
    #      key-store-location: ${KAFKA_SSL_KEYSTORE_LOCATION}
    #      trust-store-password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD}
    #      key-store-type: jks
    #      key-store-password: ${KAFKA_SSL_KEYSTORE_PASSWORD}
    ssl:
      key-password: ${KAFKA_SSL_KEY_PASSWORD}
      trust-store-location: ${KAFKA_SSL_TRUSTSTORE_LOCATION}
      key-store-location: ${KAFKA_SSL_KEYSTORE_LOCATION}
      trust-store-password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD}
      key-store-type: jks
      key-store-password: ${KAFKA_SSL_KEYSTORE_PASSWORD}
    properties:
      security.protocol: SSL
      # ssl:
      #   truststore.location: ${KAFKA_SSL_TRUSTSTORE_LOCATION}
      #   truststore.password: ${KAFKA_SSL_TRUSTSTORE_PASSWORD}
      #  keystore.location: ${KAFKA_SSL_KEYSTORE_LOCATION}
      #   keystore.password: ${KAFKA_SSL_KEYSTORE_PASSWORD}
      #   key.password: ${KAFKA_SSL_KEY_PASSWORD}
      #   endpoint.identification.algorithm: HTTPS

    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      # 手动提交
      enable-auto-commit: false
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        session.timeout.ms: 60000
    listener:
      log-container-config: false
      concurrency: 5
      # 手动提交
      ack-mode: manual_immediate
  cloud:
    nacos:
      config:
        file-extension: yml
        endpoint: ${CONFIG_CENTER_ENDPOINT}
        access-key: ${CONFIG_CENTER_ACCESS_KEY}
        secret-key: ${CONFIG_CENTER_SECRET_KEY}
        namespace: ${CONFIG_CENTER_NAMESPACE}
        group: ${CONFIG_CENTER_GROUP}
  config:
    import:
      - optional:nacos:member-tmall.yml?refreshEnabled=true&group=${CONFIG_CENTER_GROUP}
mybatis-plus:
  configuration:
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler

tenant:
  # 是否开启租户模式
  enable: true
  # 需要排除的多租户的表
  exclusionTable:
    -
  # 租户字段名称
  column: tenant_id

# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: false
  setting:
    language: zh_cn

okta:
  service:
    enable: true
    kmsPath: ${VIPMGMT_SECRET_CONFIG_PATH_OKTA}
    oktaConfigs:
      - tenantId: 53
      - tenantId: 57
forest:
  backend: okhttp3             # 后端HTTP框架（默认为 okhttp3）
  max-connections: 1000        # 连接池最大连接数（默认为 500）
  max-route-connections: 500   # 每个路由的最大连接数（默认为 500）
  max-request-queue-size: 100  # [自v1.5.22版本起可用] 最大请求等待队列大小
  max-async-thread-size: 300   # [自v1.5.21版本起可用] 最大异步线程数
  max-async-queue-size: 16     # [自v1.5.22版本起可用] 最大异步线程池队列大小
  timeout: 7000                # [已不推荐使用] 请求超时时间，单位为毫秒（默认为 3000）
  connect-timeout: 7000        # 连接超时时间，单位为毫秒（默认为 timeout）
  read-timeout: 7000           # 数据读取超时时间，单位为毫秒（默认为 timeout）
  max-retry-count: 0           # 请求失败后重试次数（默认为 0 次不重试）
  ssl-protocol: TLS            # 单向验证的HTTPS的默认TLS协议（默认为 TLS）
  log-enabled: true            # 打开或关闭日志（默认为 true）
  log-request: true            # 打开/关闭Forest请求日志（默认为 true）
  log-response-status: true    # 打开/关闭Forest响应状态日志（默认为 true）
  log-response-content: true   # 打开/关闭Forest响应内容日志（默认为 false）
  async-mode: platform         # [自v1.5.27版本起可用] 异步模式（默认为 platform）

secret.provider: ${SECRET_PROVIDER:kms}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: ["health","info","prometheus"]


# nacos rpc tls
nacos:
  remote:
    client:
      rpc:
        tls:
          enable: true
          provider: JDK
          trustAll: true

cbd:
  topics: ${KAFKA_TOPIC_CUS_Customer_CDB_publish_ALL}