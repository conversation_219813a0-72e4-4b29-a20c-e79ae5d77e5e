<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <property name="DATE_PATTERN" value="yyyy-MM-dd HH:mm:ss.SSS"/>
    <property name="PROJECT_NAME" value="market"/>
    <property name="APP_NAME" value="member-tmall"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %blue([%thread]) %cyan(%-5level) %green(%logger{50}) - %X{traceId} - %msg %marker%n"/>

    <appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="JSON_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <timestampPattern>${DATE_PATTERN}</timestampPattern>
            <timeZone>UTC</timeZone>
            <includeMdcKeyName>env</includeMdcKeyName>
            <includeMdcKeyName>project</includeMdcKeyName>
            <includeMdcKeyName>app</includeMdcKeyName>
            <includeMdcKeyName>timestamp</includeMdcKeyName>
            <includeMdcKeyName>traceId</includeMdcKeyName>
            <providers>
                <timestamp/>
                <version/>
                <loggerName/>
                <message/>
                <threadName/>
                <logLevel/>
                <callerData/>
                <stackTrace/>
                <traceId/>
                <logstashMarkers/>
                <pattern>
                    <pattern>
                        { "metadata": { "env": "${ENV}", "project": "${PROJECT_NAME}", "app": "${APP_NAME}", "timestamp": "%date{yyyy-MM-dd'T'HH:mm:ss.SSSZ}", "traceId": "%mdc{trace_id}" } }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>


    <root level="info">
        <!--<appender-ref ref="CONSOLE_LOG_PATTERN"/>-->
        <appender-ref ref="JSON_APPENDER"/>
    </root>
</configuration>