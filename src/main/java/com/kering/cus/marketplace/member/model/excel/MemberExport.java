package com.kering.cus.marketplace.member.model.excel;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/23 - 07 - 23 - 下午6:55
 * @Description: com.kering.cus.marketplace.member.model.excel
 */
@Data
public class MemberExport implements Serializable {

    /**
     *  会员OUID
     */
    private String ouid;

    /**
     * 手机号
     */
    @Alias("手机号")
    private String mobile;

    /**
     * 昵称
     */
    @Alias("昵称")
    private String nick;

    /**
     * 名
     */
    @Alias("名")
    private String firstname;

    /**
     * 姓
     */
    @Alias("姓")
    private String lastname;

    /**
     * 性别	Male / Female / Jun / Mrs / Miss / Prefer Not Say
     */
    @Alias("性别")
    private String gender;

    /**
     * 省份
     */
    @Alias("省份")
    private String province;

    /**
     * 市
     */
    @Alias("市")
    private String city;

    /**
     * cbd 会员Id
     */
    @Alias("会员Id")
    private String clientId;

    /**
     * 入会时间
     */
    @Alias("入会时间")
    private String subscribeTime;

    /**
     * 退会时间
     */
    @Alias("退会时间")
    private String unSubscribeTime;

    /**
     * 注册门店
     */
    @Alias("注册门店")
    private String registerCode;

    /**
     * 注册门店类型
     */
    @Alias("注册门店类型")
    private String registerType;

    /**
     * 注册时间
     */
    @Alias("注册时间")
    private String registerTime;

}
