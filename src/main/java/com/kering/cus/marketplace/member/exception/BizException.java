package com.kering.cus.marketplace.member.exception;

import com.kering.cus.marketplace.member.enums.IResultCode;
import lombok.Getter;

/**
 * 自定义业务异常
 *
 * <AUTHOR>
 * @date 2024/4/8 21:50
 */
@Getter
public class BizException extends RuntimeException {

    public IResultCode resultCode;

    public BizException(IResultCode errorCode) {
        super(errorCode.getMsg());
        this.resultCode = errorCode;
    }

    public BizException(String message){
        super(message);
    }

    public BizException(String message, Throwable cause){
        super(message, cause);
    }

    public BizException(Throwable cause){
        super(cause);
    }


}
