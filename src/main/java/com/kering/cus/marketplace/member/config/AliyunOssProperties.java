package com.kering.cus.marketplace.member.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@ConditionalOnProperty(value = "oss.type", havingValue = "aliyun")
@ConfigurationProperties(prefix = "oss.aliyun")
@Configuration
@Data
public class AliyunOssProperties {


    /**
     * 服务Endpoint
     */
    private String endpoint;
    /**
     * 访问凭据
     */
    private String accessKeyId;
    /**
     * 凭据密钥
     */
    private String accessKeySecret;

    /**
     * 访问前缀
     */
    private String bucketPrefix;



    private List<OssConfig> ossConfigs;

    @Data
    public static class OssConfig{
        /**
         * CDN域名
         */
        private String cdnDomain;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * 租户Id
         */
        private String tenantId;

        /**
         * 共有私有
         */
        private String bucketType;
    }
}
