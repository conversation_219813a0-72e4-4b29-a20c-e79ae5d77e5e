package com.kering.cus.marketplace.member.util;

import com.alibaba.fastjson.JSON;
import com.kering.cus.lib.secret.access.SecretAccessService;
import com.kering.cus.marketplace.member.config.TmallProperties;
import com.kering.cus.marketplace.member.model.kms.TmallResult;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/29 - 07 - 29 - 下午6:34
 * @Description: com.kering.cus.marketplace.member.util
 */
@ConditionalOnProperty(prefix = "tmall" ,name = "enable")
@Slf4j
@Component
public class TmallUtils {

    private static TmallUtils tmallUtils;
    @Resource
    private TmallProperties tmallProperties;
    @Resource
    private SecretAccessService secretAccessService;
    private static Map<String, TmallProperties.Config> configMap = new HashMap<>();
    @PostConstruct
    public void init(){
        log.info("初始化天猫店铺配置");
        tmallUtils = this;
        tmallUtils.tmallProperties = this.tmallProperties;
        if(!CollectionUtils.isEmpty(tmallUtils.tmallProperties.getConfigs())){
            List<TmallProperties.Config> configs = tmallUtils.tmallProperties.getConfigs();
            for(TmallProperties.Config config : configs){
                if(ObjectUtils.isEmpty(config.getTenantId())){
                    continue;
                }
                String kmsPath = tmallProperties.getKmsPath();
                TmallResult tmallResult =
                        secretAccessService.getSecretValue(kmsPath + config.getTenantId(), TmallResult.class);
                if(ObjectUtils.isEmpty(tmallResult)){
                    continue;
                }
                putSsoTmall(config, tmallResult,configMap);
            }
        }
    }

    private void putSsoTmall(TmallProperties.Config tmallConfig,
                             TmallResult tmallResult,
                             Map<String, TmallProperties.Config> configMap) {
        TmallProperties.Config config =
                JSON.parseObject(JSON.toJSONString(tmallConfig), TmallProperties.Config.class);
        config.setAppKey(tmallResult.getAppKey());
        config.setSecret(tmallResult.getAppSecret());
        configMap.put(config.getTenantId(), config);
    }

    public static TmallProperties.Config getTmallConfig(){
        return configMap.getOrDefault(SecurityUtils.getTenantId(),new TmallProperties.Config());
    }

}
