package com.kering.cus.marketplace.member.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.*;
import com.kering.cus.marketplace.member.config.AliyunOssProperties;
import com.kering.cus.marketplace.member.enums.OssBucketType;
import com.kering.cus.marketplace.member.enums.ResultCode;
import com.kering.cus.marketplace.member.exception.BizException;
import com.kering.cus.marketplace.member.model.FileInfoVO;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阿里云OSS服务
 *
 * <AUTHOR> Liu
 * @date 2024/4/8 21:50
 */
@Data
@Slf4j
@ConditionalOnBean(AliyunOssProperties.class)
@Component
public class AliyunOssUtil {

    public static OssBucketType PRIVATE = OssBucketType.PRIVATE;

    public static OssBucketType  PUBLIC = OssBucketType.PUBLIC;

    public static final String HTTPS = "https://";
    public static final String PATH_SEPARATOR = "/";

    @Resource
    private AliyunOssProperties aliyunOssProperties;

    @Value(value = "${file-type.allow-filter:xlsx,csv,xls,png,jpg,jpeg,gif,webp,zip}")
    private String allowFilter;


    private static AliyunOssUtil aliyunOssUtil;

    private static Map<String, OSS> ossClientMap = new HashMap<>();
    
    private static Map<String,AliyunOssProperties.OssConfig> ossConfigMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setProtocol(Protocol.HTTPS);
        aliyunOssUtil = this;
        aliyunOssUtil.aliyunOssProperties = aliyunOssProperties;
        if(CollectionUtils.isEmpty(aliyunOssProperties.getOssConfigs())){
            log.info("未配置OSS，不初始化");
            return;
        }
        for (AliyunOssProperties.OssConfig ossConfig : aliyunOssProperties.getOssConfigs()) {
            ossClientMap.put(ossConfig.getTenantId()+ossConfig.getBucketType(),
                    new OSSClientBuilder().build(aliyunOssProperties.getEndpoint(), aliyunOssProperties.getAccessKeyId(),
                            aliyunOssProperties.getAccessKeySecret(),clientBuilderConfiguration));
            ossConfigMap.put(ossConfig.getTenantId()+ossConfig.getBucketType(), ossConfig);
        }
    }



    @SneakyThrows
    public String uploadFile(BufferedImage image,OssBucketType ossBucketType) {
        OSS aliyunOssClient = getOssClient(ossBucketType);
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);

        log.info("uploadFile ossConfig{}", JSON.toJSONString(ossConfig));

        // 生成文件名(日期文件夹)
        String uuid = IdUtil.simpleUUID();
        String fileName =aliyunOssProperties.getBucketPrefix() +PATH_SEPARATOR+ DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + PATH_SEPARATOR + uuid + "." + "png";
        // 将图像输出到输出流中。

        InputStream input = null;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            ImageIO.write(image, "png", bos);
            input = new ByteArrayInputStream(bos.toByteArray());
            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("application/octet-stream");

            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest =
                    new PutObjectRequest(ossConfig.getBucketName(), fileName, input, metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);

        } catch (Exception e) {
            throw new BizException("文件上传失败");
        }finally {
            try {
                if (input != null) {
                    input.close();
                }
            } catch (IOException e) {
                log.error("error",e);
            }
        }

        String fileUrl = HTTPS + ossConfig.getBucketName() + "." + aliyunOssProperties.getEndpoint() + "/" + fileName;
        if(ossConfig.getCdnDomain()!=null){
            fileUrl = HTTPS + ossConfig.getCdnDomain()+ "/" + fileName;
        }
        return fileUrl;
    }



    @SneakyThrows
    public String uploadFile(File file,String name,OssBucketType ossBucketType) {
        OSS aliyunOssClient = getOssClient(ossBucketType);
        
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);
        
        log.info("uploadFile ossConfig{}", JSON.toJSONString(ossConfig));
        // 生成文件名(日期文件夹)
        String uuid = IdUtil.simpleUUID();
        String fileName = aliyunOssProperties.getBucketPrefix() +PATH_SEPARATOR+ DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + PATH_SEPARATOR + name;
        // 创建输出流
        ByteArrayOutputStream bos = null;
        
        // 将图像输出到输出流中。
        try( InputStream input = FileUtil.getInputStream(file)) {
            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("application/octet-stream");
            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest =
                    new PutObjectRequest(ossConfig.getBucketName(), fileName, input, metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new BizException("文件上传失败");
        }

        String fileUrl = HTTPS + ossConfig.getBucketName() + "." + aliyunOssProperties.getEndpoint() + "/" + fileName;
        if(ossConfig.getCdnDomain()!=null){
            fileUrl = HTTPS + ossConfig.getCdnDomain()+ "/" + fileName;
        }
        return fileUrl;
    }

    public boolean deleteFile(String filePath,OssBucketType ossBucketType) {
        Assert.notBlank(filePath, "删除文件路径不能为空");
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);
        OSS aliyunOssClient = getOssClient(ossBucketType);
        String fileHost = HTTPS + ossConfig.getBucketName() + "." + aliyunOssProperties.getEndpoint();
        String fileName = aliyunOssProperties.getBucketPrefix() +PATH_SEPARATOR+filePath.substring(fileHost.length() + 1);
        aliyunOssClient.deleteObject(ossConfig.getBucketName(), fileName);
        return true;
    }

    @SneakyThrows
    public String uploadFile(File file, String name, String uploadPath,OssBucketType ossBucketType) {
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);
        OSS aliyunOssClient = getOssClient(ossBucketType);
        log.info("uploadFile ossConfig{}", JSON.toJSONString(ossConfig));
        // 生成文件名(日期文件夹)
        String fileName = aliyunOssProperties.getBucketPrefix() +PATH_SEPARATOR+uploadPath + PATH_SEPARATOR +
                DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + PATH_SEPARATOR + name;
        // 创建输出流
        ByteArrayOutputStream bos = null;
        // 将图像输出到输出流中。
        try {
            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("application/octet-stream");
            metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            metadata.setObjectAcl(CannedAccessControlList.Private);
            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest =
                    new PutObjectRequest(ossConfig.getBucketName(), fileName, file, metadata);

            putObjectRequest.setMetadata(metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new BizException("文件上传失败");
        }
        return fileName;
    }

    public String getFileUrl(String filePath,int expiryTime,OssBucketType ossBucketType) {
        Assert.notBlank(filePath, "文件路径不能为空");
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);
        OSS aliyunOssClient = getOssClient(ossBucketType);
        URL url =
                aliyunOssClient.generatePresignedUrl(ossConfig.getBucketName(),
                        filePath, new Date(new Date().getTime() + expiryTime), HttpMethod.GET);
        String fileUrl = url.toString().replace(url.getHost(), ossConfig.getCdnDomain());
        log.info("getFileUrl fileUrl{}",fileUrl);
        return fileUrl;
    }

    @SneakyThrows
    public byte[] getFileBytes(String filePath, OssBucketType ossBucketType) {
        Assert.notBlank(filePath, "文件路径不能为空");
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);
        OSS aliyunOssClient = getOssClient(ossBucketType);
        OSSObject ossObject = aliyunOssClient.getObject(ossConfig.getBucketName(), filePath);
        InputStream inputStream = ossObject.getObjectContent();
        byte[] fileBytes = IOUtils.toByteArray(inputStream);
        IOUtils.close(inputStream);
        IOUtils.close(ossObject);
        return fileBytes;
    }

    /**
     * 文件上传
     * @param file
     * @param ossBucketType
     * @return
     */
    public FileInfoVO uploadFile(MultipartFile file,OssBucketType ossBucketType) {
        OSS aliyunOssClient = getOssClient(ossBucketType);
        AliyunOssProperties.OssConfig ossConfig = getOssConfig(ossBucketType);
        // 生成文件名(日期文件夹)
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        List<String> list = List.of(allowFilter.split(","));
        if(!list.contains(suffix)){
            log.error("文件类型：{}",suffix);
            throw new BizException(ResultCode.USER_UPLOAD_FILE_TYPE_ERROR);
        }

        String uuid = IdUtil.simpleUUID();
        String fileName = aliyunOssProperties.getBucketPrefix() +PATH_SEPARATOR+DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + "/" + uuid + "." + suffix;
        //  try-with-resource 语法糖自动释放流
        try (InputStream inputStream = file.getInputStream()) {

            // 设置上传文件的元信息，例如Content-Type
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            // 创建PutObjectRequest对象，指定Bucket名称、对象名称和输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucketName(), fileName, inputStream, metadata);
            // 上传文件
            aliyunOssClient.putObject(putObjectRequest);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new BizException("文件上传失败");
        }
        // 获取文件访问路径
        String fileUrl = "https://" + ossConfig.getBucketName() +
                                "." + aliyunOssProperties.getEndpoint() + "/" + fileName;
        if(ossConfig.getCdnDomain()!=null){
            fileUrl = "https://" + ossConfig.getCdnDomain()+ "/" + fileName;
        }

        FileInfoVO fileInfo = new FileInfoVO();
        fileInfo.setName(fileName);
        fileInfo.setUrl(fileUrl);
        return fileInfo;
    }



    /**
     * 获取OSS客户端
     * @return
     */
    private static OSS getOssClient(OssBucketType ossBucketType) {
        return ossClientMap.get(SecurityUtils.getTenantId()+ossBucketType.getType());
    }

    /**
     * 获取OSS配置
     * @return
     */
    private static AliyunOssProperties.OssConfig getOssConfig(OssBucketType ossBucketType) {
        return ossConfigMap.get(SecurityUtils.getTenantId()+ossBucketType.getType());
    }

}
