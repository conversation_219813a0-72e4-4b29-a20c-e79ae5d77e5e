package com.kering.cus.marketplace.member.http.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.kering.cus.marketplace.member.enums.CDBAgeRangeEnum;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CBDUserDTO implements Serializable {

    private static final String WECHAT_PLATFORM_CHANNEL = "WeChat Platform";

    private static final String TMALL = "Tmall";

    private static final String AGE_RANGE_EMPTY = "_EMPTY";
    private String brand;

    @JSONField(name = "client_id")
    private String clientId;

    private String gender;

    private Name name;

    private Phone phone;

    private AgeRang typology;

    @JSONField(name = "calculated_attributes")
    private Attributes calculatedAttributes;
    @JSONField(name = "social")
    private List<Social> socials;

    private Activity activity;


    /**
     * 年龄范围
     * @return
     */
    public String getAgeRang(){

        if(ObjectUtils.isEmpty(this.typology)){
            return null;
        }

        String ageRang = "";

        if(!ObjectUtils.isEmpty(this.typology.getAgeRange())){
            ageRang =
                    ObjectUtils.isEmpty( this.typology.getAgeRange() ) ? null : this.typology.getAgeRange();
        }

        return CDBAgeRangeEnum.getDesc(ageRang);
    }

    /**
     * 客户等级
     * @return
     */
    public String getGrade(){
        if(ObjectUtils.isEmpty(calculatedAttributes)){
            return null;
        }
        if(!ObjectUtils.isEmpty(calculatedAttributes.segmentation_FM)){
            return calculatedAttributes.segmentation_FM;
        }
        return null;
    }

    /**
     * 获取名
     * @return
     */
    public String getFirstName(){
        if(ObjectUtils.isEmpty(this.name)){
            return null;
        }
        String firstName = "";

        if(!ObjectUtils.isEmpty(this.name.getFirst())){
            firstName = ObjectUtils.isEmpty(this.name.getFirst().getLocal())? "" : this.name.getFirst().getLocal();
        }
        return firstName;
    }

    /**
     * 获取姓
     * @return
     */
    public String getLastName(){
        if(ObjectUtils.isEmpty(this.name)){
            return null;
        }
        String lastName = "";
        if(!ObjectUtils.isEmpty(this.name.getLast())){
            lastName = ObjectUtils.isEmpty(this.name.getLast().getLocal())? "" : this.name.getLast().getLocal();
        }
        return lastName;
    }

    /**
     * 获取手机号
     * @return
     */
    public String getPhone(){
        if(ObjectUtils.isEmpty(this.phone)){
            return null;
        }

        if(ObjectUtils.isEmpty(this.phone.getMobile())){
            return null;
        }

        return ObjectUtils.isEmpty(this.phone.getMobile().getNum())? "" : this.phone.getMobile().getNum();
    }

    /**
     * 获取客户归属Sa
     * @return
     */
    public  String getSaCode(){
        if(this.calculatedAttributes == null){
            return null;
        }
        if(this.calculatedAttributes.getPreferredSa() == null){
            return null;
        }
        return this.calculatedAttributes.getPreferredSa().getPrefSaCode();
    }

    /**
     * 获取客户归属门店Code
     * @return
     */
    public String getStoreCode(){
        if(this.calculatedAttributes == null){
            return null;
        }
        if(this.calculatedAttributes.getPreferredStore() == null){
            return null;
        }
        return this.calculatedAttributes.getPreferredStore().getPrefStCode();
    }

    /**
     * 获取客户归属门店名称
     * @return
     */
    public String getStoreName(){
        if(this.calculatedAttributes == null){
            return null;
        }
        if(this.calculatedAttributes.getPreferredStore() == null){
            return null;
        }
        return this.calculatedAttributes.getPreferredStore().getPrefStName();
    }

    /**
     * 获取UnionId
     * @return
     */
    public String getUnionId(){
        return getChannelClientId(WECHAT_PLATFORM_CHANNEL);
    }

    private String getChannelClientId(String channel) {
        Social social = getChannelSocial(channel);
        if(ObjectUtils.isEmpty(social)){
            return null;
        }
        return social.getUserId();
    }

    /**
     * 获取Ouid
     * @return
     */
    public String getOuId(){
        return getChannelClientId(TMALL);
    }

    /**
     * 获取昵称
     * @return
     */
    public String getNickName(){
        return getChannelClientNickName(TMALL);
    }

    public LocalDateTime getLastSubscriptionDate(){
        Social social = getChannelSocial(TMALL);
        if(ObjectUtils.isEmpty(social)){
            return null;
        }
        return social.getLastSubscriptionDate();
    }


    public LocalDateTime getLastUnSubscriptionDate(){
        Social social = getChannelSocial(TMALL);
        if(ObjectUtils.isEmpty(social)){
            return null;
        }
        return social.getLastUnSubscriptionDate();
    }

    private Social getChannelSocial(String channel){
        if(CollectionUtils.isEmpty(this.socials)){
            return null;
        }
        for (Social social : this.socials) {
            // WeChat Platform 为 微信渠道.
            if(channel.equalsIgnoreCase(social.getChannel())){
                return social;
            }
        }
        return null;
    }

    private String getChannelClientNickName(String channel) {
        Social social = getChannelSocial(channel);
        if(ObjectUtils.isEmpty(social)){
            return null;
        }
        return social.clientNickname;
    }

    @Data
    static class Name{
        @JSONField(name = "per_title")
        private String perTitle;
        private First first;
        private Last last;
    }
    @Data
    static class First{
        private String local;
    }

    @Data
    static class Last{
        private String local;
    }

    @Data
    static class Phone{
        private Mobile mobile;
    }

    @Data
    static class Mobile{
        private String pref;
        private String num;

        @JSONField(name = "val_flg")
        private String valFlg;
    }

    @Data
    static class AgeRang{
        @JSONField(name = "age_range")
        private String ageRange;
    }

    @Data
    static class Attributes{
        private String segmentation_FM;
        private String segmentation_p_1;

        @JSONField(name = "preferred_sa")
        private PreferredSa preferredSa;

        @JSONField(name = "preferred_store")
        private PreferredStore preferredStore;
    }
    @Data
    static class PreferredSa{
        @JSONField(name = "pref_sa_code")
        private String prefSaCode;
    }

    @Data
    static class PreferredStore{
        @JSONField(name = "pref_st_code")
        private String prefStCode;
        @JSONField(name = "pref_st_name")
        private String prefStName;
    }

    @Data
    static class Social{
        private String name;
        private String channel;
        @JSONField(name = "user_id")
        private String userId;
        @JSONField(name = "client_nickname")
        private String clientNickname;
        @JSONField(name = "recruitment_channel")
        private String recruitmentChannel;
        private String language;
        @JSONField(name = "follow_flag")
        private String followFlag;
        @JSONField(name = "verification_flag")
        private String verificationFlag;
        @JSONField(name = "last_subscription_date")
        private LocalDateTime lastSubscriptionDate;
        @JSONField(name = "last_unsubscription_date")
        private LocalDateTime lastUnSubscriptionDate;
    }

    @Data
    public static class Activity{
        @JSONField(name="cust_since_dt")
        private LocalDateTime custSinceDt;
        @JSONField(name="last_credit_dt")
        private LocalDateTime lastCreditDt;
        @JSONField(name="last_upd_dt")
        private LocalDateTime lastUpdDt;
        @JSONField(name="purch_behaviour")
        private String purchBehaviour;
        @JSONField(name="reg_sa_code")
        private String regSaCode;
        @JSONField(name="reg_sa_last_name")
        private String regSaLastName;
        @JSONField(name="reg_sa_first_name")
        private String regSaFirstName;
        private SalesBean sales;
        @JSONField(name="registr_store")
        private String registrStore;
        @JSONField(name="last_upd_store_id")
        private String lastUpdStoreId;
    }

    @Data
    static class SalesBean {
        private int tot;
        private int tot_12;
        private int tot_13_24;
    }
}
