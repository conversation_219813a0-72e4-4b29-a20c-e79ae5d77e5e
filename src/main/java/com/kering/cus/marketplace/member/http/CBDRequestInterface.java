package com.kering.cus.marketplace.member.http;

import com.alibaba.fastjson2.JSONObject;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

import static com.dtflys.forest.backend.ContentType.APPLICATION_JSON;

public interface CBDRequestInterface {

    @Post(value = "${url}",
            contentType = APPLICATION_JSON,
        headers = {
            "Authorization: Bearer ${accessToken}"
        })
    JSONObject request(@Var("url") String cbdUrl,
                                     @JSONBody JSONObject clientId,
                                     @Var("accessToken") String accessToken);
}
