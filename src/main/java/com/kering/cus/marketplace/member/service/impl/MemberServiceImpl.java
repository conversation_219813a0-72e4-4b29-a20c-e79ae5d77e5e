package com.kering.cus.marketplace.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kering.cus.marketplace.member.config.ECStoreProperties;
import com.kering.cus.marketplace.member.enums.OssBucketType;
import com.kering.cus.marketplace.member.enums.ResultCode;
import com.kering.cus.marketplace.member.enums.StoreTypeEnum;
import com.kering.cus.marketplace.member.enums.ThirdPartyStatusEnum;
import com.kering.cus.marketplace.member.exception.BizException;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.SecurityDto;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.model.excel.MemberExport;
import com.kering.cus.marketplace.member.model.param.MemberQueryPageParam;
import com.kering.cus.marketplace.member.model.result.MemberPageResult;
import com.kering.cus.marketplace.member.model.result.MemberResult;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.service.action.AbstractActionService;
import com.kering.cus.marketplace.member.util.AESUtils;
import com.kering.cus.marketplace.member.util.AliyunOssUtil;
import com.kering.cus.marketplace.member.util.SecurityUtils;
import com.kering.cus.marketplace.member.util.ThirdPartyTaskUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 上午10:20
 * @Description: com.kering.cus.marketplace.member.service.impl
 */
@Slf4j
@Service
public class MemberServiceImpl implements MemberService {
    private static final int MAX_PAGE_SIZE = 100;
    private static final int FILE_SIZE = 50000;
    private static final String FILE_PREFIX = "member_data_export_";
    private static final String FILE_SUFFIX = ".csv";
    private static final int FILE_RANDOM_NUMBERS = 8;

    private static final String UPLOAD_PATH = "member";
    @Resource
    private BizMemberMapper bizMemberMapper;
    @Resource
    private AliyunOssUtil aliyunOssUtil;
    @Resource
    private ECStoreProperties eCStoreProperties;

    @Override
    public IPage<MemberPageResult> getMemberPageList(MemberQueryPageParam param) {
        // 参数校验
        if (param == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        Page<BizMember> memberPage = getMemberPage(param);
        List<MemberPageResult> memberPageResults =
                JSON.parseArray(JSON.toJSONString(memberPage.getRecords()), MemberPageResult.class);
        desensitizationMobile(memberPageResults);
        IPage<MemberPageResult> result =
                new Page<>(memberPage.getTotal(), memberPageResults.size(),memberPage.getTotal());
        result.setRecords(memberPageResults);
        return result;
    }

    private Page<BizMember> getMemberPage(MemberQueryPageParam param) {
        int pageSize = Math.min(Math.max(param.getPageSize(), 1), MAX_PAGE_SIZE);
        Page<BizMember> page = new Page<>(Math.max(param.getPageNum(), 1), pageSize);
        return bizMemberMapper.selectPage(page, new LambdaQueryWrapper<BizMember>()
                .eq(!ObjectUtils.isEmpty(param.getOuid()), BizMember::getOuid, param.getOuid())
                .like(!ObjectUtils.isEmpty(param.getNick()), BizMember::getNick, param.getNick())
                .eq(!ObjectUtils.isEmpty(param.getMobile()), BizMember::getMobile, AESUtils.encrypt(param.getMobile()))
                .eq(!ObjectUtils.isEmpty(param.getClientId()), BizMember::getClientId, param.getClientId())
                .ge(!ObjectUtils.isEmpty(param.getSubscribeStartTime()), BizMember::getSubscribeTime,
                        parseDateTime(param.getSubscribeStartTime()))
                .le(!ObjectUtils.isEmpty(param.getSubscribeEndTime()), BizMember::getSubscribeTime,
                        parseDateTime(param.getSubscribeEndTime()))
                .ne(BizMember::getKanonymousFlag,Boolean.TRUE)
        );
    }

    /**
     * 进行手机号脱敏。
     * @param memberPageResults
     */
    private void desensitizationMobile(List<MemberPageResult> memberPageResults) {
        if(CollectionUtils.isEmpty(memberPageResults)){
            return;
        }
        for (MemberPageResult result : memberPageResults) {
            if(ObjectUtils.isEmpty(result.getMobile())){
                continue;
            }
            String phone = DesensitizedUtil.mobilePhone(result.getMobile());
            result.setMobile(phone);
        }
    }

    private Date parseDateTime(String dateTimeStr) {
        if (ObjectUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        return DateUtil.parse(dateTimeStr);
    }

    @Override
    public MemberResult getMemberDetail(String id) {
        BizMember member = getMember(id);
        MemberResult result =
                JSON.parseObject(JSON.toJSONString(member), MemberResult.class);
        result.setMobile(DesensitizedUtil.mobilePhone(result.getMobile()));
        return result;
    }

    private BizMember getMember(String id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new BizException(ResultCode.MEMBER_ID_IS_EMPTY);
        }
        BizMember member = bizMemberMapper.selectById(id);
        if(ObjectUtils.isEmpty(member)){
            throw new BizException(ResultCode.MEMBER_IS_EMPTY);
        }
        return member;
    }

    @Override
    public String getOriginalMobile(String id) {
        BizMember member = getMember(id);
        return member.getMobile();
    }

    @Async("taskExecutor")
    @Override
    public void export(MemberQueryPageParam param, String taskId, SecurityDto securityDto) {
        //进行导出逻辑.
        List<File> fileList = new ArrayList<>();
        try {
            SecurityUtils.init(securityDto);
            executeExport(param, fileList);
            String fileName = FILE_PREFIX + DateUtil.currentSeconds() +
                    RandomUtil.randomNumbers(FILE_RANDOM_NUMBERS) + ".zip";
            File file = FileUtil.newFile(fileName);
            ZipUtil.zip(file,false, fileList.toArray(new File[0]));
            String url = uploadOSS(file);
            ThirdPartyTaskUtil.updateTask(taskId,url, ThirdPartyStatusEnum.COMPLETED,"完成");
        }catch (Exception e){
            log.error("导出任务失败:",e);
            ThirdPartyTaskUtil.updateTask(taskId, ThirdPartyStatusEnum.ERROR,"导出失败");
        }finally {
            log.info("导出结束");
            delFile(fileList);
            SecurityUtils.clear();
        }
    }

    @Override
    public void updateMemberByCDB(CBDUserDTO cbdUserDTO) {
        String ouId = cbdUserDTO.getOuId();
        BizMember member = bizMemberMapper.selectOne(
                new LambdaQueryWrapper<BizMember>()
                        .eq(BizMember::getOuid, ouId));
        if(ObjectUtils.isEmpty(member)){
            return;
        }
        BizMember bizMember = AbstractActionService.assembleMember(cbdUserDTO);
        bizMember.setId(member.getId());
        assembleRegisterInfo(bizMember,cbdUserDTO);
        bizMemberMapper.updateById(bizMember);
    }

    private void assembleRegisterInfo(BizMember bizMember, CBDUserDTO cbdUserDTO) {
        CBDUserDTO.Activity activity = cbdUserDTO.getActivity();
        if(ObjectUtils.isEmpty(activity)){
            return;
        }
        bizMember.setRegisterCode(activity.getRegistrStore());
        bizMember.setRegisterTime(DateUtil.date(activity.getCustSinceDt()));
        bizMember.setRegisterCode(getStoreType(activity.getRegistrStore()));
    }

    private void delFile(List<File> fileList) {
        for (File file : fileList) {
            try {
                FileUtil.del(file);
            }catch (Exception e){
                log.error("删除临时文件异常",e);
            }
        }
    }

    private void executeExport(MemberQueryPageParam param, List<File> fileList) {
        List<BizMember> records;
        param.setPageSize(MAX_PAGE_SIZE);
        CsvWriter writer = null;
        int fileNum = 1;
        long currentSeconds = DateUtil.currentSeconds();
        String randomNumbers = RandomUtil.randomNumbers(FILE_RANDOM_NUMBERS);
        List<MemberExport> writerList = new ArrayList<>();
        AtomicInteger fileCount = new AtomicInteger(0);
        do {
            records = getMemberPage(param).getRecords();
            List<MemberExport> exportList =
                    assembleExport(records);
            writerList.addAll(exportList);
            if(param.getPageNum() != 1
                    && CollectionUtils.isEmpty(writerList)){
                break;
            }

            if(ObjectUtils.isEmpty(writer)){
                String fileName = FILE_PREFIX + currentSeconds + randomNumbers
                        +"-" + fileNum + FILE_SUFFIX;
                File file = FileUtil.newFile(fileName);
                writer = CsvUtil.getWriter(file, StandardCharsets.UTF_8);
                fileList.add(file);
            }

            if(CollectionUtils.isEmpty(writerList)){
                MemberExport memberExport = new MemberExport();
                Map<String,Object> map = BeanUtil.beanToMap(memberExport);
                writer.writeHeaderLine(map.keySet().toArray(new String[0]));
                writer.flush();
                break;
            }

            writerList = writeInFile(writerList,writer,fileCount);
            if(fileCount.get() == FILE_SIZE){
                writer = null;
                fileCount.set(0);
            }
            param.setPageNum(param.getPageNum()+1);
        }while (!CollectionUtils.isEmpty(records));

        if(!ObjectUtils.isEmpty(writer)){
            writer.close();
        }
    }


    /**
     * 写入文件
     *
     */
    private List<MemberExport> writeInFile(List<MemberExport> list,
                                           CsvWriter writer, AtomicInteger fileCount) {
        List<MemberExport> writeList = list;
        List<MemberExport> resultList = new ArrayList<>();
        int count = fileCount.get();
        if(writeList.size() + count > FILE_SIZE){
            writeList = list.subList(0,FILE_SIZE - count);
            resultList = list.subList(FILE_SIZE - count,list.size());
        }
        writeBeans(writeList,writer,count == 0);
        fileCount.addAndGet(writeList.size());
        return resultList;
    }

    private void writeBeans(List<?> beans,CsvWriter writer,boolean isFirst){
        if (CollectionUtils.isEmpty(beans)) {
            return;
        }
        if(isFirst){
            writer.writeBeans(beans);
            return;
        }
        Map<String, Object> map;
        for (Object bean : beans) {
            map = BeanUtil.beanToMap(bean);
            writer.writeLine(Convert.toStrArray(map.values()));
        }
        writer.flush();
    }

    private String uploadOSS(File file) {
        try {
            String url =
                    aliyunOssUtil.uploadFile(file, file.getName(),UPLOAD_PATH, OssBucketType.PRIVATE);
            log.info("下载预约记录上传OSS结束");
            return url;
        }catch (Exception e) {
            log.error("上传oss异常",e);
            throw e;
        }
    }

    private List<MemberExport> assembleExport(List<BizMember> records) {
        if(CollectionUtils.isEmpty(records)){
            return List.of();
        }
        List<MemberExport> exportList =
                JSON.parseArray(JSON.toJSONString(records), MemberExport.class);

        for (MemberExport memberExport : exportList) {
            if(ObjectUtils.isEmpty(memberExport.getMobile())){
                continue;
            }
            memberExport.setMobile(DesensitizedUtil.mobilePhone(memberExport.getMobile()));
        }
        return exportList;
    }

    private String getStoreType(String storeCode){
        if(ObjectUtils.isEmpty(storeCode)){
            return null;
        }

        List<ECStoreProperties.Tenant> tenants =
                eCStoreProperties.getList();

        if(CollectionUtils.isEmpty(tenants)){
            return StoreTypeEnum.Others.name();
        }

        Map<String, List<String>> map =
                tenants.stream().collect(Collectors.toMap(ECStoreProperties.Tenant::getTenantId,
                        ECStoreProperties.Tenant::getStoreCodes));

        List<String> storeList = map.get(SecurityUtils.getTenantId());

        return (storeList.contains(storeCode)?StoreTypeEnum.EC:StoreTypeEnum.Others).name();

    }
}
