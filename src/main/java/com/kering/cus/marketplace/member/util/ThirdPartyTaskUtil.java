package com.kering.cus.marketplace.member.util;

import com.kering.cus.marketplace.member.config.ThirdPartyTaskProperties;
import com.kering.cus.marketplace.member.enums.ThirdPartyStatusEnum;
import com.kering.cus.marketplace.member.http.ThirdPartyTaskInterface;
import com.kering.cus.marketplace.member.http.dto.ThirdPartyTaskVo;
import com.kering.cus.marketplace.member.http.form.ThirdPartyTaskInsertForm;
import com.kering.cus.marketplace.member.http.form.ThirdPartyTaskUpdateForm;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@ConditionalOnProperty(prefix = "third-party-task.service" ,name = "enable")
@Slf4j
@Component
public class ThirdPartyTaskUtil {
    private static ThirdPartyTaskUtil thirdPartyTaskUtil;
    @Autowired
    private ThirdPartyTaskProperties thirdPartyTaskProperties;

    @Autowired
    private ThirdPartyTaskInterface thirdPartyTaskInterface;

    private static final Map<String, ThirdPartyTaskProperties.AppConfig> appConfigMap = new HashMap<>();
    @PostConstruct
    public void init() {
        log.info("初始化ThirdPartyTaskUtil");
        thirdPartyTaskUtil = this;
        thirdPartyTaskUtil.thirdPartyTaskProperties = this.thirdPartyTaskProperties;
        thirdPartyTaskUtil.thirdPartyTaskInterface = this.thirdPartyTaskInterface;
        for (ThirdPartyTaskProperties.AppConfig appConfig : thirdPartyTaskProperties.getAppConfigs()) {
            appConfigMap.put(appConfig.getTenantId(), appConfig);
        }
    }

    /**
     * 创建任务列表.
     * @return
     */
    public static String createTask(String title, String type, String linkType, ThirdPartyStatusEnum status){
        if(ObjectUtils.isEmpty(getConfig())){
            log.info("未配置对应租户，不进行创建第三方任务");
            return "";
        }

        ThirdPartyTaskInsertForm insertForm = new ThirdPartyTaskInsertForm();
        insertForm.setTitle(title);
        insertForm.setLinkType(linkType);
        insertForm.setStatus(status.name());
        insertForm.setIsSendMessage(true);
        insertForm.setType(type);
        insertForm.setProgress(0);
        insertForm.setDescription("task create");
        ThirdPartyTaskVo task =
                thirdPartyTaskUtil.thirdPartyTaskInterface.createTask(
                        SecurityUtils.getTenantId(), SecurityUtils.getUserEmail(), insertForm);
        return task.getId();
    }
    /**
     * 更新任务列表.
     * @return
     */
    public static void updateTask(String taskId,ThirdPartyStatusEnum status){
        if(ObjectUtils.isEmpty(getConfig())){
            log.info("未配置对应租户，不进行创建第三方任务");
            return ;
        }
        ThirdPartyTaskUpdateForm updateForm = new ThirdPartyTaskUpdateForm();
        updateForm.setId(taskId);
        updateForm.setStatus(status.name());
        thirdPartyTaskUtil.thirdPartyTaskInterface.updateTask(SecurityUtils.getTenantId(),
                SecurityUtils.getUserEmail(),updateForm);
    }

    /**
     * 更新任务列表.
     * @return
     */
    public static void updateTask(String taskId,ThirdPartyStatusEnum status,String description){
        if(ObjectUtils.isEmpty(getConfig())){
            log.info("未配置对应租户，不进行创建第三方任务");
            return ;
        }
        ThirdPartyTaskUpdateForm updateForm = new ThirdPartyTaskUpdateForm();
        updateForm.setId(taskId);
        updateForm.setStatus(status.name());
        updateForm.setDescription(description);
        thirdPartyTaskUtil.thirdPartyTaskInterface.updateTask(SecurityUtils.getTenantId(),
                SecurityUtils.getUserEmail(),updateForm);
    }

    /**
     * 更新任务列表.
     * @return
     */
    public static void updateTask(String taskId,String linkUrl,ThirdPartyStatusEnum status,String description){
        if(ObjectUtils.isEmpty(getConfig())){
            log.info("未配置对应租户，不进行创建第三方任务");
            return ;
        }
        ThirdPartyTaskUpdateForm updateForm = new ThirdPartyTaskUpdateForm();
        updateForm.setId(taskId);
        String downloadUrl =
                thirdPartyTaskUtil.thirdPartyTaskProperties.getDownloadUrl();
        updateForm.setLinkUrl(downloadUrl+linkUrl);
        updateForm.setStatus(status.name());
        updateForm.setProgress(100);
        if(ThirdPartyStatusEnum.ERROR.equals(status)){
            updateForm.setProgress(0);
        }
        updateForm.setDescription(description);
        thirdPartyTaskUtil.thirdPartyTaskInterface.updateTask(SecurityUtils.getTenantId(),
                SecurityUtils.getUserEmail(),updateForm);
    }

    /**
     *
     * @return
     */
    private static ThirdPartyTaskProperties.AppConfig getConfig(){
        return appConfigMap.get(SecurityUtils.getTenantId());
    }
}
