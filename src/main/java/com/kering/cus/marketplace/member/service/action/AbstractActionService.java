package com.kering.cus.marketplace.member.service.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import com.kering.cus.marketplace.member.mapper.BizMemberMapper;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import com.kering.cus.marketplace.member.service.ActionService;
import com.kering.cus.marketplace.member.util.CBDUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 下午2:50
 * @Description: com.kering.cus.marketplace.member.service.action
 */
@Slf4j
public abstract class AbstractActionService implements ActionService {

    private static final String EMPTY = "N/A";
    @Resource
    BizMemberMapper bizMemberMapper;

    protected JSONObject cdbQuery(String body){
        JSONObject result = JSON.parseObject(body);
        String bizRequest = result.getString("bizRequest");
        JSONObject bizRequestJson = JSON.parseObject(bizRequest);
        JSONObject queryResult = CBDUtils.query(bizRequestJson);
        Assert.isTrue(!queryResult.containsKey("errors"),
                "查询失败异常：" + queryResult.get("errors"));
        return queryResult;
    }
    protected JSONObject cdbMutation(String body){
        JSONObject result = JSON.parseObject(body);
        String bizRequest = result.getString("bizRequest");
        JSONObject bizRequestJson = JSON.parseObject(bizRequest);
        JSONObject mutation = CBDUtils.mutation(bizRequestJson);
        Assert.isTrue(!mutation.containsKey("errors"),
                "处理失败异常：" + mutation.get("errors"));
        return mutation;
    }

    protected CBDUserDTO getCBDUserDTOByCBDRequest(String body){
        JSONObject result = JSON.parseObject(body);
        String bizRequest = result.getString("bizRequest");
        JSONObject bizRequestJson = JSON.parseObject(bizRequest);
        return bizRequestJson.getJSONObject("variables")
                            .getObject("data", CBDUserDTO.class);
    }

    protected List<CBDUserDTO> getCBDUserDTOByCBDResponse(JSONObject response){
        return response.getJSONObject("data")
                .getJSONObject("getCustomersBySocial")
                .getJSONArray("customers")
                .toJavaList(CBDUserDTO.class);
    }

    protected void saveMember(CBDUserDTO cbdUserDTO){
        if(bizMemberMapper.exists(new LambdaQueryWrapper<BizMember>()
                .eq(BizMember::getOuid,cbdUserDTO.getOuId()))){
            this.updateMember(cbdUserDTO);
            return;
        }
        this.insertMember(cbdUserDTO);
    }

    protected void insertMember(CBDUserDTO cbdUserDTO){
        BizMember bizMember = assembleMember(cbdUserDTO);
        bizMember.setCreatedDate(DateUtil.date());
        bizMember.setCreatedBy("system");
        bizMemberMapper.insert(bizMember);
    }

    public static BizMember assembleMember(CBDUserDTO cbdUserDTO) {
        BizMember bizMember = new BizMember();
        bizMember.setOuid(cbdUserDTO.getOuId());
        bizMember.setNick(cbdUserDTO.getNickName());
        bizMember.setMobile(cbdUserDTO.getPhone());
        bizMember.setGender(cbdUserDTO.getTitle());
        bizMember.setClientId(cbdUserDTO.getClientId());
        bizMember.setFirstname(EMPTY.equals(cbdUserDTO.getFirstName())? "" : cbdUserDTO.getFirstName());
        bizMember.setUnSubscribeTime(DateUtil.date(cbdUserDTO.getLastUnSubscriptionDate()));
        bizMember.setSubscribeTime(DateUtil.date(cbdUserDTO.getLastSubscriptionDate()));
        bizMember.setLastname(EMPTY.equals(cbdUserDTO.getLastName())? "" : cbdUserDTO.getLastName());
        bizMember.setModifiedDate(DateUtil.date());
        bizMember.setModifiedBy("system");
        bizMember.setKanonymousFlag(Boolean.FALSE);
        bizMember.setCity(cbdUserDTO.getCity());
        bizMember.setProvince(cbdUserDTO.getProvince());
        isSubscribe(bizMember);
        return bizMember;
    }

    protected static void isSubscribe(BizMember bizMember){
        if(ObjectUtils.isEmpty(bizMember.getSubscribeTime())){
            bizMember.setIsSubscribe(Boolean.FALSE);
            return ;
        }
        if(!ObjectUtils.isEmpty(bizMember.getUnSubscribeTime()) &&
                bizMember.getUnSubscribeTime().after(bizMember.getSubscribeTime())){
            bizMember.setIsSubscribe(Boolean.FALSE);
            return ;
        }
        bizMember.setIsSubscribe(Boolean.TRUE);
    }

    protected void updateMember(CBDUserDTO cbdUserDTO){
        BizMember bizMember = assembleMember(cbdUserDTO);
        BizMember member =
                bizMemberMapper.selectOne(
                        new LambdaQueryWrapper<BizMember>()
                                .eq(BizMember::getOuid, bizMember.getOuid()));
        bizMember.setId(member.getId());
        bizMemberMapper.updateById(bizMember);
    }

    protected void updateMemberByClientId(CBDUserDTO cbdUserDTO){
        BizMember bizMember = assembleMember(cbdUserDTO);
        BizMember member =
                bizMemberMapper.selectOne(
                        new LambdaQueryWrapper<BizMember>()
                                .eq(BizMember::getClientId, bizMember.getClientId()));
        bizMember.setId(member.getId());
        bizMemberMapper.updateById(bizMember);
    }

}
