package com.kering.cus.marketplace.member.http.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class OktaTokenDTO implements Serializable {

    /**
     * token 类型
     */
    @JSONField(name = "token_type")
    private String tokenType;

    /**
     * token
     */
    @JSONField(name = "access_token")
    private String accessToken;

    /**
     * 有效期时长
     */
    @JSONField(name = "expires_in")
    private Integer expiresIn;

    /**
     * token 使用范围
     */
    @JSONField(name = "scope")
    private String scope;


}
