package com.kering.cus.marketplace.member.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.IoUtils;
import com.kering.cus.marketplace.member.enums.TenantIdEnum;
import com.kering.cus.marketplace.member.service.QimenService;
import com.kering.cus.marketplace.member.util.TenantContext;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/21 - 07 - 21 - 下午2:28
 * @Description: com.kering.cus.marketplace.member.controller
 */
@Slf4j
@RestController
@RequestMapping("qimen")
public class QimenController {

    @Resource
    private QimenService qimenService;

    /**
     * 奇门callback接口。
     */
    @PostMapping("callback")
    public JSONObject callback(HttpServletRequest request, HttpServletResponse response) throws Exception{
        JSONObject jsonObject = new JSONObject();
        String body = IoUtils.toString(request.getInputStream(), StandardCharsets.UTF_8.toString());
        String brand =
                request.getParameter("brand");
        String tenantId
                = TenantIdEnum.getTenantIdByBrand(brand);
        TenantContext.setCurrentTenant(tenantId);
        String action = request.getParameter("action");
        if (!qimenService.checkSign(request,body)) {
            log.error("签名验证失败");
            jsonObject.put("flag","failure");
            jsonObject.put("sub_code","sign-check-filure");
            jsonObject.put("sub_message","Illegal request");
            return jsonObject;
        }
        log.info("验证通过");
        jsonObject.put("bizResponse",qimenService.execute(body,action));
        return jsonObject;
    }
}
