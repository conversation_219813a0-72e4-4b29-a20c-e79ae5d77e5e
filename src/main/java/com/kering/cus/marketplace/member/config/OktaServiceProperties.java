package com.kering.cus.marketplace.member.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23 21:06
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "okta.service")
@ConditionalOnProperty(prefix = "okta.service" ,name = "enable")
@DependsOn({"objectMapperConfig"})
public class OktaServiceProperties {
    private List<OktaConfig> oktaConfigs;

    private String kmsPath;

    @Getter
    @Setter
    public static class OktaConfig {
        /**
         * tenantId
         */
        private String tenantId;

        /**
         * client_id
         */
        private String clientId;

        /**
         * secret
         */
        private String clientSecret;

        /**
         * token地址
         */
        private String tokenUrl;

        /**
         * 用户信息查询地址
         */
        private String userInfoUrl;

        /**
         * 设置私钥
         */
        private String privateKey;

        private String oauth2TokenUrl;
    }
}
