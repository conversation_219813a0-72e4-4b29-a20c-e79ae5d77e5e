package com.kering.cus.marketplace.member.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础分页请求对象
 *
 * <AUTHOR>
 * @date 2024/4/8 21:50
 */
@Data
@Schema 
public class BasePageQuery implements Serializable {

    @Schema(description = "页码", example = "1")
    private int pageNum = 1;

    @Schema(description = "每页记录数", example = "10")
    private int pageSize = 10;
}
