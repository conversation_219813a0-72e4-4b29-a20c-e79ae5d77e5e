package com.kering.cus.marketplace.member.kafka.consumer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.config.CDBProperties;
import com.kering.cus.marketplace.member.enums.TenantIdEnum;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.util.TenantContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;


/**
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 下午4:49
 * @Description: com.kering.cus.marketplace.member.kafka.consumer
 */
@Component
@Slf4j
@RefreshScope
public class CBDConsumer {

    @Resource
    private MemberService memberService;

    @Resource
    private CDBProperties CDBProperties;

    @KafkaListener(topics = "#{@CDBProperties.topics}",
            groupId = "#{CDBProperties.group}", containerFactory = "ackContainerFactory")
    public void handleMessage(ConsumerRecord consumerRecord, Acknowledgment acknowledgment) {
        try {
            String message =
                    (String) consumerRecord.value();
            log.info("topic:{}",consumerRecord.topic());
            log.info("cdb kafka message:{}",message);
            JSONObject jsonObject = JSON.parseObject(message);
            JSONObject metadata = jsonObject.getJSONObject("metadata");
            String brand = metadata.getString("brand");
            String tenantId = TenantIdEnum.getTenantIdByBrand(brand);
            TenantContext.setCurrentTenant(tenantId);
            CBDUserDTO userDTO =
                    jsonObject.getJSONObject("data")
                            .toJavaObject(CBDUserDTO.class);
            memberService.updateMemberByCDB(userDTO);
        } catch (Exception e) {
            log.error("ERROR", e);
        } finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }
    }
}
