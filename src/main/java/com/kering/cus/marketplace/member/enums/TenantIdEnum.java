package com.kering.cus.marketplace.member.enums;

import lombok.Getter;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/4 - 07 - 04 - 上午10:57
 * @Description: com.d1m.common.web.enums
 */
@Getter
public enum TenantIdEnum {

    YSL("53","ys","SLP"),
    BAL("57","ba","BAL"),
    BV("58","bv","BV"),
    BRIONI("63","br","BR"),
    AMQ("55","amq","AMQ"),
    ;
    private String tenantId;
    private String schema;
    private String organizationBrand;

    TenantIdEnum(String tenantId, String schema, String organizationBrand){
        this.tenantId = tenantId;
        this.schema = schema;
        this.organizationBrand =organizationBrand;
    }

    public static String getTenantId(String schema){
        for (TenantIdEnum value : TenantIdEnum.values()) {
            if(value.schema.equals(schema)){
                return value.tenantId;
            }
        }
        return null;
    }

    public static String getTenantIdByBrand(String brand){
        for (TenantIdEnum value : TenantIdEnum.values()) {
            if(value.organizationBrand.equals(brand)){
                return value.tenantId;
            }
        }
        return null;
    }

}
