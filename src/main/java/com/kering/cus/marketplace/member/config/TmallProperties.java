package com.kering.cus.marketplace.member.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.List;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/29 - 07 - 29 - 上午11:10
 * @Description: com.kering.cus.marketplace.member.config
 */
@Configuration
@ConditionalOnProperty(prefix = "tmall",name = "enable")
@ConfigurationProperties(prefix = "tmall")
@Data
@DependsOn({"objectMapperConfig"})
public class TmallProperties {

    private String kmsPath;
    private List<Config> configs;

    @Data
    public static class Config{
        private String tenantId;

        private String appKey;

        private String secret;
    }

}
