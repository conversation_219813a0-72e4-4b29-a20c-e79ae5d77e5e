package com.kering.cus.marketplace.member.util;

import com.kering.cus.marketplace.member.model.SecurityDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;


/**
 * Spring Security 工具类
 *
 * <AUTHOR>
 * @date 2024/4/8 21:50
 */
@Slf4j
public class SecurityUtils {
    private static final ThreadLocal<SecurityDto> threadLocal = new ThreadLocal<>();

    private SecurityUtils(){}

    /**
     * 获取租户Id
     * @return
     */
    public static String getTenantId() {
        String tenantId = getSecurityDto().getTenantId();
        //为null时，尝试从Local线程池的上下文变量中去取
        if(tenantId==null){
            tenantId = TenantContext.getCurrentTenant();
        }else{
            TenantContext.setCurrentTenant(tenantId);
        }
        return tenantId;
    }
    public static Long getUserId() {
        return getSecurityDto().getUserId();
    }

    public static String getUsername() {
        return getSecurityDto().getUserName();
    }

    /**
     * 获取登录userEmail
     * @return
     */
    public static String getUserEmail(){
        return getSecurityDto().getEmail();
    }

    /**
     * 获取名称
     * @return
     */
    public static String getName(){
        return getSecurityDto().getName();
    }

    /**
     * 进行初始化当前线程 SecurityUtils
     * @param securityDto
     */
    public static void init(SecurityDto securityDto){
        threadLocal.set(securityDto);
        TenantContext.setCurrentTenant(securityDto.getTenantId());
    }

    /**
     * 获取当前线程的基础数据.
     * @return
     */
    public static SecurityDto getSecurityDto(){
        if(ObjectUtils.isEmpty(threadLocal.get())){
            return new SecurityDto();
        }
        return threadLocal.get();
    }

    /**
     * 清除当前线程的数据。
     */
    public static void clear(){
        threadLocal.remove();
        TenantContext.clear();
    }

}
