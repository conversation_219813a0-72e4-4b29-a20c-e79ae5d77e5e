package com.kering.cus.marketplace.member.enums;

import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: guoyichi
 * @Date: 2025/8/8 - 08 - 08 - 下午5:17
 * @Description: com.kering.cus.marketplace.member.enums
 */
@Getter
public enum TitleEnum {

    MS("女士"),
    MRS("太太"),
    MISS("小姐"),
    MR("先生"),
    ABSENT("君"),
    PREFER_NOT_SAY("不方便透露")
    ;

    private String name;

    private static Map<String,TitleEnum> map;

    static {
        map = new HashMap<>();

        map.put("MS.",MS);
        map.put("MRS.",MRS);
        map.put("MISS.",MISS);
        map.put("MR.",MR);
        map.put("PREFER NOT SAY",PREFER_NOT_SAY);
    }
    TitleEnum(String name){
        this.name= name;
    }

    public static  TitleEnum getTitleEnum(String name) {
        for (TitleEnum enumConstant : TitleEnum.values()) {
            if (enumConstant.name().equals(name)) {
                return enumConstant;
            }
        }
        return null;
    }


    public static String getTitleDesc(String code){
        if(ObjectUtils.isEmpty(code)){
            return "-";
        }
        TitleEnum titleEnum = TitleEnum.getTitleEnum(code);
        if(!ObjectUtils.isEmpty(titleEnum)){
            return titleEnum.name;
        }
        titleEnum = map.get(code);
        if(ObjectUtils.isEmpty(titleEnum)){
            return "-";
        }
        return titleEnum.name;
    }


}
