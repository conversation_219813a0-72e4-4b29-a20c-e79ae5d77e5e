package com.kering.cus.marketplace.member.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/28 - 07 - 28 - 下午2:16
 * @Description: com.kering.cus.marketplace.member.config
 */
@ConditionalOnProperty(prefix = "ec",name = "enable")
@ConfigurationProperties(prefix = "ec")
@Configuration
@Data
public class ECStoreProperties {

    private List<Tenant> list;

    @Data
    public static class Tenant{
        private String tenantId;
        private List<String> storeCodes;
    }
}
