package com.kering.cus.marketplace.member.mybatis.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.result.ResultMapException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class ListStringHandler extends BaseTypeHandler<List<String>> {

    protected String getTypeName(Object o) {
        if (o instanceof String) {
            return "varchar";
        } else {
            throw new ResultMapException(
                    "ListStringHandler only fit to convert List<String> to varchar[], "
            );
        }
    }

    public static List<String> objectToList(Object arrayObj) {
        try {
            String[] list = (String[]) arrayObj;
            return Arrays.asList(list);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public List<String> getDownCastingList(Array array) {
        if (array == null) {
            return null;
        }
        try {
            return objectToList(array.getArray());
        } catch (SQLException throwable) {
            log.error("error:",throwable);
            return null;
        }
    }

    public String getType(List<String> parameter) {
        if (parameter == null || parameter.isEmpty()) {
            return "varchar";
        }
        Object o = parameter.get(0);
        return getTypeName(o);
    }


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        String type = getType(parameter);
        Connection conn = ps.getConnection();
        Array array = conn.createArrayOf(type, parameter.toArray());
        ps.setArray(i, array);
    }

    @Override
    public List<String> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return getDownCastingList(resultSet.getArray(s));
    }

    @Override
    public List<String> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return getDownCastingList(resultSet.getArray(i));
    }

    @Override
    public List<String> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return getDownCastingList(callableStatement.getArray(i));
    }

}