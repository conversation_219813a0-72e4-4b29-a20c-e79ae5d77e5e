package com.kering.cus.marketplace.member.interceptor;


import com.alibaba.fastjson2.JSON;
import com.kering.cus.lib.common.PlatformHeaders;
import com.kering.cus.marketplace.member.model.SecurityDto;
import com.kering.cus.marketplace.member.util.SecurityUtils;
import com.kering.cus.marketplace.member.util.TenantContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class WebHttpInterceptor implements HandlerInterceptor {

    private static final String AUTHORIZATION = "authorization";

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {
        //获取请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String,String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if(name.equals(AUTHORIZATION)){
                continue;
            }
            headerMap.put(name,request.getHeader(name));
        }
        log.info("请求头信息:{}", JSON.toJSONString(headerMap));
        //租户Id
        String tenantId = request.getHeader(PlatformHeaders.TENANT_ID.getValue());
        //先进行设置租户Id.
        TenantContext.setCurrentTenant(tenantId);
        //员工邮箱
        String employeeEmail = request.getHeader(PlatformHeaders.EMPLOYEE_EMAIL.getValue());
        //用户Id
        String userId = request.getHeader(PlatformHeaders.USER_ID.getValue());
        //需要在这里构建Security 的参数.
        setCacheAndGet(tenantId,userId,employeeEmail);
        log.info("SecurityUtils数据:{}", SecurityUtils.getSecurityDto());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request,
                                HttpServletResponse response,
                                Object handler, Exception ex) throws Exception {
        SecurityUtils.clear();
    }

    private void setCacheAndGet(String tenantId, String userId, String employeeEmail){
        SecurityDto securityDto = new SecurityDto();
        securityDto.setTenantId(tenantId);
        securityDto.setEmail(employeeEmail);
        securityDto.setUserName(userId);
        SecurityUtils.init(securityDto);
    }


}
