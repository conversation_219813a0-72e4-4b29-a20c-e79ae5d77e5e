package com.kering.cus.marketplace.member.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.config.TmallProperties;
import com.kering.cus.marketplace.member.constants.Constants;
import com.kering.cus.marketplace.member.factory.ActionExecuteFactoty;
import com.kering.cus.marketplace.member.service.ActionService;
import com.kering.cus.marketplace.member.service.QimenService;
import com.kering.cus.marketplace.member.util.SignUtil;
import com.kering.cus.marketplace.member.util.TmallUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 上午11:26
 * @Description: com.kering.cus.marketplace.member.service.impl
 */
@Slf4j
@Service
public class QimenServiceImpl implements QimenService {

    @Resource
    private ActionExecuteFactoty actionExecuteFactoty;


    @Override
    public boolean checkSign(HttpServletRequest request, String body) throws IOException {
        TmallProperties.Config tmallConfig = TmallUtils.getTmallConfig();
        Map<String, String> params = new HashMap<>();
        Map<String, String> headerMap = getHeaderMap(request, StandardCharsets.UTF_8.toString());
        Map<String, String> queryMap = getQueryMap(request, StandardCharsets.UTF_8.toString());
        params.putAll(headerMap);
        params.putAll(queryMap);
        String localSign = SignUtil.signTopRequest(params, body, tmallConfig.getSecret(), Constants.SIGN_METHOD_MD5);
        String remoteSign = queryMap.get("sign");
        return localSign.equals(remoteSign);
    }

    @Override
    public String execute(String body, String action) {
        ActionService execute =
                actionExecuteFactoty.getAction(action);
        try {
            return JSON.toJSONString(execute.execute(body));
        }catch (Exception e){
            log.error("执行异常",e);
            JSONObject fail = new JSONObject();
            fail.put("status","failed");
            fail.put("msg",e.getMessage());
            return fail.toString();
        }

    }


    public Map<String, String> getHeaderMap(HttpServletRequest request, String charset) throws IOException {
        Map<String, String> headerMap = new HashMap<>();
        String signList = request.getHeader("top-sign-list");
        if (!ObjectUtils.isEmpty(signList)) {
            for (String key : signList.split(",")) {
                String value = request.getHeader(key);
                if (ObjectUtils.isEmpty(value)) {
                    headerMap.put(key, "");
                } else {
                    headerMap.put(key, URLDecoder.decode(value, charset));
                }
            }
        }
        return headerMap;
    }

    public  Map<String, String> getQueryMap(HttpServletRequest request, String charset) throws IOException {
        Map<String, String> queryMap = new HashMap<>();
        String queryString = request.getQueryString();
        String[] params = queryString.split("&");
        for (String param : params) {
            String[] kv = param.split("=");
            String key;
            if (kv.length == 2) {
                key = URLDecoder.decode(kv[0], charset);
                String value = URLDecoder.decode(kv[1], charset);
                queryMap.put(key, value);
            } else if (kv.length == 1) {
                key = URLDecoder.decode(kv[0], charset);
                queryMap.put(key, "");
            }
        }
        return queryMap;
    }

}
