package com.kering.cus.marketplace.member.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@ConditionalOnProperty(prefix = "third-party-task.service",name = "enable")
@ConfigurationProperties(prefix = "third-party-task.service")
@Configuration
@Data
public class ThirdPartyTaskProperties {
    private String host;
    private String downloadUrl;
    private List<AppConfig> appConfigs;
    @Data
    public static class AppConfig {
        private String tenantId;
    }

}
