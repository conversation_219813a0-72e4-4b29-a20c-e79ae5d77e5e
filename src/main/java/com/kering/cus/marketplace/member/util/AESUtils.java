package com.kering.cus.marketplace.member.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;

/**
 * AES加密
 * <AUTHOR>
 */
@Slf4j
public class AESUtils {
    private static final SymmetricCrypto AES;

    static {
        //随机生成密钥
        byte[] key = HexUtil.decodeHex("f552f1b3e8a5087bd3d85787db615f09");
        //构建
        AES = new SymmetricCrypto(SymmetricAlgorithm.AES, key);
    }

    /**
     * 加密为16进制表示
     */
    public static String encrypt(String value) {
        String val = value;
        // 尝试先解密
        if(StrUtil.isNotEmpty(value)){
            val = AES.encryptHex(decrypt(value));
        }
        return val;
    }

    /**
     * 解密为字符串
     */
    public static String decrypt(String value) {
        String val = value;
        try {
            val = AES.decryptStr(value, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e){
            log.info("AES.decryptStr error, use origin value.");
        }
        return val;
    }
}
