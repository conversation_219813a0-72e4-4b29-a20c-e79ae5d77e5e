package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import org.springframework.stereotype.Service;

/** 创建CBD会员
 * @Auther: guoyichi
 * @Date: 2025/7/23 - 07 - 23 - 下午5:22
 * @Description: com.kering.cus.marketplace.member.service.action
 */
@Service("createMemberActionServiceImpl")
public class CreateMemberActionServiceImpl extends AbstractActionService {
    @Override
    public JSONObject execute(String body) {
        JSONObject cbdResult =
                super.cdbMutation(body);
        String clientId =
                cbdResult.getJSONObject("data")
                            .getJSONObject("createCustomer")
                            .getString("clientId");
        CBDUserDTO cbdUserDTO =
                super.getCBDUserDTOByCBDRequest(body);
        cbdUserDTO.setClientId(clientId);
        super.saveMember(cbdUserDTO);
        return cbdResult;
    }
}
