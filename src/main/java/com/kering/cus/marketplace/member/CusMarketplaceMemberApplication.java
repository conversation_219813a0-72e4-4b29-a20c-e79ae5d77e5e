package com.kering.cus.marketplace.member;

import com.dtflys.forest.springboot.annotation.ForestScan;
import com.kering.cus.lib.persistence.common.config.JDBCDataSourceConfig;
import com.kering.cus.lib.persistence.common.config.KafkaSslFileInitializer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = {"com.kering.cus.marketplace.member",
        "com.kering.cus.lib.secret.access", "com.kering.cus.lib.common"})
@EnableDiscoveryClient
@EnableAsync
@EnableTransactionManagement
@ForestScan(basePackages = "com.kering.cus.marketplace.member.http")
@SuppressWarnings("all")
@Import(JDBCDataSourceConfig.class)
public class CusMarketplaceMemberApplication {

    public static void main(String[] args) {
        KafkaSslFileInitializer.initKafkaSslFile();
        SpringApplication.run(CusMarketplaceMemberApplication.class, args);
    }

}
