package com.kering.cus.marketplace.member.http.dto;

import lombok.Data;

import java.util.List;

@Data
public class CusClientInfoDTO {
    private String id;
    private String clientId;
    private String tenantId;
    private String refId;
    private Integer validityPeriod;
    private String type;
    private String aclStrategyMode;
    private String tokenCid;
    private String remark;
    private String name;
    private Boolean disabled;
    private String externalId;
    private String modifiedDate;
    private String modifiedBy;
    private List<String> userLoginNames;

}
