package com.kering.cus.marketplace.member.enums;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 自定义响应码
 *
 * <AUTHOR>
 * @date 2024/4/8 21:50
 **/
@AllArgsConstructor
@NoArgsConstructor
public enum ResultCode implements IResultCode, Serializable {
    SUCCESS("00000", "success"),
    REPEAT_SUBMIT_ERROR("10303", "您的请求已提交，请不要重复提交或等待片刻再尝试。"),

    PARAM_ERROR("10400", "用户请求参数错误"),
    RESOURCE_NOT_FOUND("10401", "请求资源不存在"),
    PARAM_IS_NULL("10410", "请求必填参数为空"),

    SYSTEM_EXECUTION_ERROR("20001", "系统执行出错"),
    SYSTEM_EXECUTION_TIMEOUT("20100", "系统执行超时"),
    FLOW_LIMITING("20210", "系统限流"),
    DEGRADATION("20220", "系统功能降级"),

    SYSTEM_RESOURCE_ERROR("20300", "系统资源异常"),
    SYSTEM_RESOURCE_EXHAUSTION("20310", "系统资源耗尽"),
    SYSTEM_RESOURCE_ACCESS_ERROR("20320", "系统资源访问异常"),
    SYSTEM_READ_DISK_FILE_ERROR("20321", "系统读取磁盘文件失败"),

    CALL_THIRD_PARTY_SERVICE_ERROR("30001", "调用第三方服务出错"),
    MIDDLEWARE_SERVICE_ERROR("30100", "中间件服务出错"),
    INTERFACE_NOT_EXIST("30113", "接口不存在"),

    MESSAGE_SERVICE_ERROR("30120", "消息服务出错"),
    MESSAGE_DELIVERY_ERROR("30121", "消息投递出错"),
    MESSAGE_CONSUMPTION_ERROR("30122", "消息消费出错"),
    MESSAGE_SUBSCRIPTION_ERROR("30123", "消息订阅出错"),
    MESSAGE_GROUP_NOT_FOUND("30124", "消息分组未查到"),

    DATABASE_ERROR("30300", "数据库服务出错"),
    DATABASE_TABLE_NOT_EXIST("30311", "表不存在"),
    DATABASE_COLUMN_NOT_EXIST("30312", "列不存在"),
    DATABASE_DUPLICATE_COLUMN_NAME("30321", "多表关联中存在多个相同名称的列"),
    DATABASE_DEADLOCK("30331", "数据库死锁"),
    DATABASE_PRIMARY_KEY_CONFLICT("30341", "主键冲突"),
    DATABASE_ILLEGAL_ARGUMENT("30351", "数据库参数非法"),
    MEMBER_ID_IS_EMPTY("40000","会员id为空"),
    MEMBER_IS_EMPTY("40000","会员数据不存在"),

    USER_UPLOAD_FILE_TYPE_ERROR("10704", "用户上传文件类型未匹配"),
    ;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    private String code;

    private String msg;

    @Override
    public String toString() {
        return "{" +
                "\"code\":\"" + code + '\"' +
                ", \"msg\":\"" + msg + '\"' +
                '}';
    }


    public static ResultCode getValue(String code){
        for (ResultCode value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return SYSTEM_EXECUTION_ERROR; // 默认系统执行错误
    }
}
