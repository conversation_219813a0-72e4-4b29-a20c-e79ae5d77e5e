package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import org.springframework.stereotype.Service;

/** 匿名化处理
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 下午4:27
 * @Description: com.kering.cus.marketplace.member.service.action
 */
@Service("anonymousMemberActionServiceImpl")
public class AnonymousMemberActionServiceImpl extends AbstractActionService{
    @Override
    public JSONObject execute(String body) {
        String bizRequest =
                JSON.parseObject(body).getString("bizRequest");
        JSONObject result = JSONObject.parseObject(bizRequest);
        String clientId = result.getString("clientId");
        bizMemberMapper.update(new LambdaUpdateWrapper<BizMember>()
                .eq(BizMember::getClientId,clientId)
                .set(BizMember::getNick,null)
                .set(BizMember::getMobile,null)
                .set(BizMember::getSubscribeTime,null)
                .set(BizMember::getUnSubscribeTime,null)
                .set(BizMember::getCity,null)
                .set(BizMember::getProvince,null)
                .set(BizMember::getFirstname,null)
                .set(BizMember::getLastname,null)
                .set(BizMember::getGender,null)
                .set(BizMember::getKanonymousFlag,Boolean.TRUE)
        );
        return null;
    }
}
