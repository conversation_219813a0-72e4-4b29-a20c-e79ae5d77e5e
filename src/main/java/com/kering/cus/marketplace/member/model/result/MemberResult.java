package com.kering.cus.marketplace.member.model.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kering.cus.marketplace.member.annotation.EncryptedColumn;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 下午2:41
 * @Description: com.kering.cus.marketplace.member.model.result
 */
@Data
public class MemberResult implements Serializable {

    private String id;

    /**
     *  会员OUID
     */
    private String ouid;

    /**
     * 手机号
     */
    @EncryptedColumn
    private String mobile;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 名
     */
    @EncryptedColumn
    private String firstname;

    /**
     * 姓
     */
    @EncryptedColumn
    private String lastname;

    /**
     * 性别	MALE / FEMALE / JUN / MRS / MISS / PREFER_NOT_SAY
     */
    private String gender;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * cbd 会员Id
     */
    private String clientId;

    /**
     * 入会时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date subscribeTime;

    /**
     * 退会时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unSubscribeTime;

    /**
     * 注册门店
     */
    private String registerCode;
    /**
     * 注册门店类型
     */
    private String registerType;
    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    /**
     * 是否入会
     */
    private Boolean isSubscribe;
}
