package com.kering.cus.marketplace.member.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import com.kering.cus.marketplace.member.model.SecurityDto;
import com.kering.cus.marketplace.member.model.param.MemberQueryPageParam;
import com.kering.cus.marketplace.member.model.result.MemberPageResult;
import com.kering.cus.marketplace.member.model.result.MemberResult;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 上午10:19
 * @Description: com.kering.cus.marketplace.member.service
 */
public interface MemberService {
    IPage<MemberPageResult> getMemberPageList(MemberQueryPageParam param);

    MemberResult getMemberDetail(String id);

    String getOriginalMobile(String id);

    void export(MemberQueryPageParam param, String taskId, SecurityDto securityDto);

    void updateMemberByCDB(CBDUserDTO message);
}
