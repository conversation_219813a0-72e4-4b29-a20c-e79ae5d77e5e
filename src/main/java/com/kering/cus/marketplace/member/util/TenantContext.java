package com.kering.cus.marketplace.member.util;

/**
 * <AUTHOR>
 * @date 2024/4/8 23:44
 */
public class TenantContext {
    private TenantContext(){}
    private static ThreadLocal<String> currentTenant = new ThreadLocal<>();

    public static String getCurrentTenant() {
        return currentTenant.get();
    }

    public static void setCurrentTenant(String tenant) {
        currentTenant.set(tenant);
    }

    public static void clear() {
        currentTenant.remove();
    }
}
