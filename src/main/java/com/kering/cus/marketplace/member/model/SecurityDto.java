package com.kering.cus.marketplace.member.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SecurityDto implements Serializable {

    private Long userId;

    private String tenantId;

    private String userName;

    private String name;

    private List<Long> wxCpDeptId;

    private String email;

    private String saCode;

    private String source;

    private String jtl;

    private String agentId;
}
