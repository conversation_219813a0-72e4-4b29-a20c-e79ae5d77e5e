package com.kering.cus.marketplace.member.http.form;

import lombok.Data;

import java.io.Serializable;

@Data
public class ThirdPartyTaskInsertForm implements Serializable {

    /**
     * Title
     */
    private String title;
    private String type;
    /**
     * (READY: pending, PAUSED: pausing, PROCESSING: processing, COMPLETED: completed, CANCELED: cancelled, ERROR: exception)
     */
    private String status;
    /**
     * 进度 0-100
     */
    private Integer progress;
    /**
     * 描述
     */
    private String description;
    /**
     * Link type (DOWNLOAD: Download, FORWARD: Jump)
     */
    private String linkType;
    /**
     * 路径地址
     */
    private String linkUrl;
    /**
     * 是否发生消息提醒。
     */
    private Boolean isSendMessage;
}
