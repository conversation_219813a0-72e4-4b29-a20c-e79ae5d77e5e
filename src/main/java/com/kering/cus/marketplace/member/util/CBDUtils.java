package com.kering.cus.marketplace.member.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.config.CDBProperties;
import com.kering.cus.marketplace.member.http.CBDRequestInterface;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CBD工具类
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "cdb", name = "enable")
public class CBDUtils {
    private static CBDUtils CBDUtils;
    @Resource
    private CBDRequestInterface cbdRequestInterface;

    @Resource
    private CDBProperties cdbProperties;

    @PostConstruct
    public void init() {
        log.info("初始化CBDUtils");
        CBDUtils = this;
        CBDUtils.cbdRequestInterface = cbdRequestInterface;
        CBDUtils.cdbProperties = cdbProperties;
    }

    public static JSONObject query(JSONObject body){
        return CBDUtils.cbdRequestInterface.request(
                CBDUtils.cdbProperties.getQueryUrl(),body,OktaUtil.getOktaOauth2Token());
    }

    public static JSONObject mutation(JSONObject body){
        return CBDUtils.cbdRequestInterface.request(
                CBDUtils.cdbProperties.getMutationUrl(),body,OktaUtil.getOktaOauth2Token());
    }
}
