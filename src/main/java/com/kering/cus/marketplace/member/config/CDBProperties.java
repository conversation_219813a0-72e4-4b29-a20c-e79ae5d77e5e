package com.kering.cus.marketplace.member.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConditionalOnProperty(prefix = "cdb",name = "enable")
@ConfigurationProperties(prefix = "cdb")
public class CDBProperties {
    /**
     * 操作地址
     */
    private String mutationUrl;
    /**
     * 查询地址
     */
    private String queryUrl;

    private List<String> topics;

    private String group;
}
