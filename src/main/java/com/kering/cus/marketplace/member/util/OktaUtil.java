package com.kering.cus.marketplace.member.util;

import com.alibaba.fastjson.JSON;
import com.kering.cus.lib.secret.access.SecretAccessService;
import com.kering.cus.marketplace.member.config.OktaServiceProperties;
import com.kering.cus.marketplace.member.http.OktaRequestInterface;
import com.kering.cus.marketplace.member.http.dto.OktaTokenDTO;
import com.kering.cus.marketplace.member.http.dto.OktaUserDTO;
import com.kering.cus.marketplace.member.model.kms.OktaResult;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

@ConditionalOnProperty(prefix = "okta.service" ,name = "enable")
@Slf4j
@Component
public class OktaUtil {
    private static final String GRANT_TYPE = "client_credentials";

    private static final String OAUTH_V2_TOKEN_SCOPE = "default_scope";

    private static final String OAUTH = "OAUTH";

    private static OktaUtil oktaUtil;
    @Autowired
    private SecretAccessService secretAccessService;

    @Autowired
    private OktaRequestInterface oktaRequestInterface;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OktaServiceProperties oktaServiceProperties;

    private static Map<String, OktaServiceProperties.OktaConfig> configMap = new HashMap<>();

    @PostConstruct
    public void init(){
        oktaUtil = this;
        oktaUtil.oktaRequestInterface = this.oktaRequestInterface;
        oktaUtil.stringRedisTemplate = this.stringRedisTemplate;
        oktaUtil.oktaServiceProperties = this.oktaServiceProperties;
        if(!CollectionUtils.isEmpty(oktaUtil.oktaServiceProperties.getOktaConfigs())){
            List<OktaServiceProperties.OktaConfig> oktaConfigs =
                    oktaUtil.oktaServiceProperties.getOktaConfigs();

            for(OktaServiceProperties.OktaConfig oktaConfig : oktaConfigs){
                if(ObjectUtils.isEmpty(oktaConfig.getTenantId())){
                    continue;
                }
                String kmsPath = oktaUtil.oktaServiceProperties.getKmsPath();
                OktaResult oktaResult =
                        secretAccessService.getSecretValue(kmsPath + oktaConfig.getTenantId(), OktaResult.class);
                if(ObjectUtils.isEmpty(oktaResult)){
                    continue;
                }
                putSsoOkta(oktaConfig, oktaResult,configMap);
            }
        }
    }

    private void putSsoOkta(OktaServiceProperties.OktaConfig oktaConfig,
                            OktaResult oktaResult,
                            Map<String, OktaServiceProperties.OktaConfig> configMap) {
        OktaServiceProperties.OktaConfig config =
                JSON.parseObject(JSON.toJSONString(oktaConfig), OktaServiceProperties.OktaConfig.class);
        config.setClientId(oktaResult.getOktaSsoClientId());
        config.setClientSecret(oktaResult.getOktaSsoClientSecret());
        configMap.put(config.getTenantId()+"-"+OAUTH, config);
    }

    /**
     * 获取okta OAUTH2 token
     * @return
     */
    public static String getOktaOauth2Token(){
        try {
            String tokenValue =
                    oktaUtil.stringRedisTemplate.opsForValue().get("member-oktaOauth2Token:" + TenantContext.getCurrentTenant());
            if(!ObjectUtils.isEmpty(tokenValue)){
                return tokenValue;
            }
        }catch (Exception e){
            log.error("获取OKTA OAUTH2 TOKEN 缓存 失败",e);
        }

        OktaServiceProperties.OktaConfig oktaConfig =
                configMap.get(TenantContext.getCurrentTenant()+"-"+OAUTH);

        OktaTokenDTO token = oktaUtil.oktaRequestInterface.getOktaOAuth2Token(oktaConfig.getOauth2TokenUrl(),
                oktaConfig.getClientId(), oktaConfig.getClientSecret(), GRANT_TYPE, OAUTH_V2_TOKEN_SCOPE);
        try {
            oktaUtil.stringRedisTemplate.opsForValue()
                    .set("member-oktaOauth2Token:"+TenantContext.getCurrentTenant(),token.getAccessToken(),
                            3, TimeUnit.MINUTES);
        }catch (Exception e){
            log.error("设置OKTA OAUTH2 缓存 TOKEN失败",e);
        }

        return token.getAccessToken();
    }
}
