package com.kering.cus.marketplace.member.enums;

import lombok.Getter;

/**
 * 中国省份枚举
 */
@Getter
public enum ProvinceEnum {
    // 中国大陆省份（带前缀）
    CHINESE_MAINLAND__ANHUI("安徽省"),
    CHINESE_MAINLAND__BEIJING("北京"),
    CHINESE_MAINLAND__CHONGQING("重庆"),
    CHINESE_MAINLAND__FUJIAN("福建省"),
    CHINESE_MAINLAND__GANSU("甘肃省"),
    CHINESE_MAINLAND__GUANGDONG("广东省"),
    CHINESE_MAINLAND__GUANGXI("广西壮族自治区"),
    CHINESE_MAINLAND__GUIZHOU("贵州省"),
    CHINESE_MAINLAND__HAINAN("海南省"),
    CHINESE_MAINLAND__HEBEI("河北省"),
    CHINESE_MAINLAND__HEILONGJIANG("黑龙江省"),
    CHINESE_MAINLAND__HENAN("河南省"),
    CHINESE_MAINLAND__HUBEI("湖北省"),
    CHINESE_MAINLAND__HUNAN("湖南省"),
    CHINESE_MAINLAND__JIANGSU("江苏省"),
    CHINESE_MAINLAND__JIANGXI("江西省"),
    CHINESE_MAINLAND__JILIN("吉林省"),
    CHINESE_MAINLAND__LIAONING("辽宁省"),
    CHINESE_MAINLAND__NEI_MONGOL("内蒙古自治区"),
    CHINESE_MAINLAND__NINGXIA_HUI("宁夏回族自治区"),
    CHINESE_MAINLAND__QINGHAI("青海省"),
    CHINESE_MAINLAND__SHAANXI("陕西省"),
    CHINESE_MAINLAND__SHANDONG("山东省"),
    CHINESE_MAINLAND__SHANGHAI("上海"),
    CHINESE_MAINLAND__SHANXI("山西省"),
    CHINESE_MAINLAND__SICHUAN("四川省"),
    CHINESE_MAINLAND__TIANJIN("天津"),
    CHINESE_MAINLAND__XINJIANG_UYGUR("新疆维吾尔自治区"),
    CHINESE_MAINLAND__XIZANG("西藏自治区"),
    CHINESE_MAINLAND__YUNNAN("云南省"),
    CHINESE_MAINLAND__ZHEJIANG("浙江省"),

    // 特别行政区/台湾/海外（带前缀）
    TAIWAN_REGION__EMPTY("台湾"),
    HONG_KONG_SAR__EMPTY("香港特别行政区"),
    MACAU_SAR__EMPTY("澳门特别行政区"),
    OUT_OF_COUNTRY__EMPTY("海外"),

    // 中国大陆省份（无前缀）
    ANHUI("安徽省"),
    BEIJING("北京"),
    CHONGQING("重庆"),
    FUJIAN("福建省"),
    GANSU("甘肃省"),
    GUANGDONG("广东省"),
    GUANGXI("广西壮族自治区"),
    GUIZHOU("贵州省"),
    HAINAN("海南省"),
    HEBEI("河北省"),
    HEILONGJIANG("黑龙江省"),
    HENAN("河南省"),
    HUBEI("湖北省"),
    HUNAN("湖南省"),
    JIANGSU("江苏省"),
    JIANGXI("江西省"),
    JILIN("吉林省"),
    LIAONING("辽宁省"),
    NEI_MONGOL("内蒙古自治区"),
    NINGXIA_HUI("宁夏回族自治区"),
    QINGHAI("青海省"),
    SHAANXI("陕西省"),
    SHANDONG("山东省"),
    SHANGHAI("上海"),
    SHANXI("山西省"),
    SICHUAN("四川省"),
    TIANJIN("天津"),
    XINJIANG_UYGUR("新疆维吾尔自治区"),
    XIZANG("西藏自治区"),
    YUNNAN("云南省"),
    ZHEJIANG("浙江省");
    private final String name;
    ProvinceEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static ProvinceEnum getProvinceEnum(String name) {
        for (ProvinceEnum enumConstant : ProvinceEnum.values()) {
            if (enumConstant.name().equals(name)) {
                return enumConstant;
            }
        }
        return null;
    }
}
