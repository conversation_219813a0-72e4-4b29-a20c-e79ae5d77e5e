package com.kering.cus.marketplace.member.model.result;

import com.kering.cus.marketplace.member.annotation.EncryptedColumn;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 上午11:35
 * @Description: com.kering.cus.marketplace.member.model.result
 */
@Data
public class MemberPageResult implements Serializable {

    private String id;
    /**
     * ouid
     */
    private String ouid;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * cdbId
     */
    private String clientId;

    /**
     * 名
     */
    @EncryptedColumn
    private String firstname;

    /**
     * 姓
     */
    @EncryptedColumn
    private String lastname;

    /**
     * 性别	MALE / FEMALE / JUN / MRS / MISS / PREFER_NOT_SAY
     */
    private String gender;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

}
