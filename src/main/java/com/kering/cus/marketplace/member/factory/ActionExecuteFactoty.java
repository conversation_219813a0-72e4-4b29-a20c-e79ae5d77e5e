package com.kering.cus.marketplace.member.factory;

import com.kering.cus.marketplace.member.service.ActionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 上午11:21
 * @Description: com.kering.cus.marketplace.member.factory
 */
@Component
public class ActionExecuteFactoty {
    @Resource
    private Map<String, ActionService> factory;

    private static final Map<String,String> mapping;

    static {
        mapping  = new HashMap<>();
        mapping.put("createCustomer", "createMemberActionServiceImpl");
        mapping.put("getCustomersBySocial", "getMemberActionServiceImpl");
        mapping.put("updateCustomer", "updateMemberActionServiceImpl");
        mapping.put("initCustomer", "initMemberActionServiceImpl");
        mapping.put("anonymousCustomer", "anonymousMemberActionServiceImpl");
    }
    public ActionService getAction(String action) {
        return factory.get(mapping.get(action));
    }

}
