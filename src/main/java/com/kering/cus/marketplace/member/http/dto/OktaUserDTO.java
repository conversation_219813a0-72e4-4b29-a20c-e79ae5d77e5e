package com.kering.cus.marketplace.member.http.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class OktaUserDTO implements Serializable {


    private String id;
    private String status;
    private String created;
    private String activated;
    private String statusChanged;
    private String lastLogin;
    private String lastUpdated;
    private Object passwordChanged;
    private TypeBean type;
    private ProfileBean profile;
    private CredentialsBean credentials;
    @JSONField(name = "_links")
    private LinksBean links;

    @Data
    public static class TypeBean {
        /**
         * id : oty90tf7dkx3PmiVm416
         */
        private String id;
    }
    @Data
    public static class ProfileBean {

        private String lastName;
        private String supervisoryOrganization;
        private String login;
        private String firstName;
        private Object mobilePhone;
        private String region;
        private String email;
    }
    @Data
    public static class CredentialsBean {
        /**
         * provider : {"type":"ACTIVE_DIRECTORY","name":"apac.guccigroup.dom"}
         */

        private ProviderBean provider;
    }
    @Data
    public static class LinksBean {
        /**
         * self : {"href":"https://signin.kering.com/api/v1/users/00ucqhfiu4EfWRxR4417"}
         */

        private SelfBean self;
    }

    @Data
    public static class ProviderBean {
        /**
         * type : ACTIVE_DIRECTORY
         * name : apac.guccigroup.dom
         */

        private String type;
        private String name;
    }

    @Data
    public static class SelfBean {

        private String href;

        public String getHref() {
            return href;
        }

        public void setHref(String href) {
            this.href = href;
        }
    }
}
