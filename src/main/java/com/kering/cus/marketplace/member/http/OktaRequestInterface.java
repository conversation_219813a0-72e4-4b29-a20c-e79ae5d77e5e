package com.kering.cus.marketplace.member.http;

import com.dtflys.forest.annotation.*;
import com.kering.cus.marketplace.member.http.dto.OktaTokenDTO;


public interface OktaRequestInterface {

    /**
     * 获取OAuth 2.0 token
     * @param url
     * @param clientId
     * @param clientSecret
     * @param grantType
     * @param scope
     */
    @PostRequest(value = "${url}",
            contentType = "application/x-www-form-urlencoded")
    OktaTokenDTO getOktaOAuth2Token(@Var("url")String url,
                               @Body( "client_id")String clientId,
                               @Body( "client_secret")String clientSecret,
                               @Body( "grant_type")String grantType,
                               @Body( "scope")String scope);
}
