package com.kering.cus.marketplace.member.http;

import com.dtflys.forest.annotation.*;
import com.kering.cus.marketplace.member.http.dto.ThirdPartyTaskVo;
import com.kering.cus.marketplace.member.http.form.ThirdPartyTaskInsertForm;
import com.kering.cus.marketplace.member.http.form.ThirdPartyTaskUpdateForm;


@BaseRequest(baseURL ="#{third-party-task.service.host}")
public interface ThirdPartyTaskInterface {

    @Post(value = "/app-tasks")
    ThirdPartyTaskVo createTask(@Header("X-Cus-Tenant-Id") String tenantId,
                                @Header("X-CUS-Employee-Email")String email,
                                @JSONBody ThirdPartyTaskInsertForm insertForm);

    @Put(value = "/app-tasks")
    String updateTask(@Header("X-Cus-Tenant-Id") String tenantId,
                      @Header("X-CUS-Employee-Email")String email,
                      @JSONBody ThirdPartyTaskUpdateForm insertForm);
}
