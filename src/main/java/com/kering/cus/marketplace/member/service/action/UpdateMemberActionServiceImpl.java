package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.http.dto.CBDUserDTO;
import org.springframework.stereotype.Service;

/** 更新 CBD会员
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 下午4:19
 * @Description: com.kering.cus.marketplace.member.service.action
 */
@Service("updateMemberActionServiceImpl")
public class UpdateMemberActionServiceImpl extends AbstractActionService{
    @Override
    public JSONObject execute(String body) {
        JSONObject cbdResult =
                super.cdbMutation(body);
        String clientId =
                cbdResult.getJSONObject("data")
                        .getJSONObject("updateCustomer")
                        .getString("clientId");
        CBDUserDTO cbdUserDTO =
                super.getCBDUserDTOByCBDRequest(body);
        cbdUserDTO.setClientId(clientId);
        super.updateMemberByClientId(cbdUserDTO);
        return cbdResult;
    }
}
