package com.kering.cus.marketplace.member.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.kering.cus.marketplace.member.enums.OssBucketType;
import com.kering.cus.marketplace.member.enums.ThirdPartyStatusEnum;
import com.kering.cus.marketplace.member.model.param.MemberQueryPageParam;
import com.kering.cus.marketplace.member.model.result.MemberPageResult;
import com.kering.cus.marketplace.member.model.result.MemberResult;
import com.kering.cus.marketplace.member.model.result.PageResult;
import com.kering.cus.marketplace.member.model.result.Result;
import com.kering.cus.marketplace.member.service.MemberService;
import com.kering.cus.marketplace.member.util.AliyunOssUtil;
import com.kering.cus.marketplace.member.util.SecurityUtils;
import com.kering.cus.marketplace.member.util.ThirdPartyTaskUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;

/**
 * @menu 会员后台接口
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 上午10:18
 * @Description: com.kering.cus.marketplace.member.controller
 */
@Slf4j
@RequestMapping("backend/member")
@RestController
public class BackendMemberController {
    private static final String TITLE = "会员数据导出";
    private static final String TYPE = "DOWNLOAD_FILE";
    private static final String LINK_TYPE ="DOWNLOAD";
    public static final String ZIP = ".ZIP";
    @Resource
    private MemberService memberService;

    @Resource
    private AliyunOssUtil aliyunOssUtil;

    /**
     * 获取会员列表.
     * @param param
     * @return
     */
    @GetMapping()
    public PageResult<MemberPageResult> getMemberPageList(@ParameterObject MemberQueryPageParam param){
        return PageResult.success(memberService.getMemberPageList(param));
    }

    /**
     * 会员详情.
     * @param id
     * @return
     */
    @GetMapping("{id}")
    public Result<MemberResult> getMemberDetail(@PathVariable String id){
        return Result.success(memberService.getMemberDetail(id));
    }

    /**
     * 查看原始手机号
     * @param id
     * @return
     */
    @GetMapping("original/mobile/{id}")
    public Result<String> getOriginalMobile(@PathVariable String id){
        return Result.success(memberService.getOriginalMobile(id));
    }

    /**
     * 数据导出
     * @return
     */
    @GetMapping("export")
    public Result<Void> export(@ParameterObject MemberQueryPageParam param){
        String taskId =
                ThirdPartyTaskUtil.createTask(TITLE, TYPE, LINK_TYPE, ThirdPartyStatusEnum.READY);
        memberService.export(param,taskId, SecurityUtils.getSecurityDto());
        return Result.success();
    }

    /**
     * 下载第三方任务文件。
     * @param url
     * @param response
     * @throws IOException
     */
    @GetMapping("/download/third-party")
    public void downloadThirdParty(@RequestParam("url")String url,
                                   HttpServletResponse response )throws IOException{
        String fileName =
                DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_FORMAT) + ZIP;
        byte[] fileBytes =
                aliyunOssUtil.getFileBytes(url, OssBucketType.PRIVATE);
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition,Content-Type");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + fileName);
        try (OutputStream outputStream = response.getOutputStream()) {
            IOUtils.write(fileBytes, outputStream);
        }
    }
}
