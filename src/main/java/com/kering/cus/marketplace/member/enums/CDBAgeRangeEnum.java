package com.kering.cus.marketplace.member.enums;

import lombok.Getter;

@Getter
public enum CDBAgeRangeEnum {
    _EMPTY(null),
    FROM_18_TO_24("18-24"),
    FROM_25_TO_29("25-29"),
    FROM_30_TO_34("30-34"),
    FROM_35_TO_39("35-39"),
    FROM_40_TO_44("40-44"),
    FROM_45_TO_49("45-49"),
    FROM_50_TO_54("50-54"),
    FROM_55_TO_59("55-59"),
    ABOVE_60("60+"),
    UNDER_18("UNDER 18")
;
    private String desc;
    CDBAgeRangeEnum(String desc){
        this.desc = desc;
    }

    public static String getDesc(String name){
        for (CDBAgeRangeEnum e : CDBAgeRangeEnum.values()) {
            if (e.name().equals(name)){
                return e.desc;
            }
        }
        return null;
    }
}
