package com.kering.cus.marketplace.member.service.action;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kering.cus.marketplace.member.model.entity.BizMember;
import org.springframework.stereotype.Service;

/** 初始化 本地会员信息
 * @Auther: guoyichi
 * @Date: 2025/7/25 - 07 - 25 - 下午4:26
 * @Description: com.kering.cus.marketplace.member.service.action
 */
@Service("initMemberActionServiceImpl")
public class InitMemberActionServiceImpl extends AbstractActionService{
    @Override
    public JSONObject execute(String body) {
        String bizRequest = JSON.parseObject(body).getString("bizRequest");
        BizMember member = JSONObject.parseObject(bizRequest, BizMember.class);
        bizMemberMapper.insert(member);
        return null;
    }
}
