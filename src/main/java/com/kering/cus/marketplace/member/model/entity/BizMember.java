package com.kering.cus.marketplace.member.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.kering.cus.marketplace.member.annotation.EncryptedColumn;
import com.kering.cus.marketplace.member.annotation.EncryptedTable;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 上午10:31
 * @Description: com.kering.cus.marketplace.member.model.entity
 */
@Data
@EncryptedTable
public class BizMember implements Serializable {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     *  会员OUID
     */
    private String ouid;

    /**
     * 手机号
     */
    @EncryptedColumn
    private String mobile;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 名
     */
    @EncryptedColumn
    private String firstname;

    /**
     * 姓
     */
    @EncryptedColumn
    private String lastname;

    /**
     * 性别	Male / Female / Jun / Mrs / Miss / Prefer Not Say
     */
    private String gender;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * cbd 会员Id
     */
    private String clientId;

    /**
     * 入会时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date subscribeTime;

    /**
     * 退会时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unSubscribeTime;
    /**
     * 注册门店
     */
    private String registerCode;
    /**
     * 注册门店类型
     */
    private String registerType;
    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    private Boolean kanonymousFlag;

    private String tenantId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedDate;

    private String createdBy;

    private String modifiedBy;

}
