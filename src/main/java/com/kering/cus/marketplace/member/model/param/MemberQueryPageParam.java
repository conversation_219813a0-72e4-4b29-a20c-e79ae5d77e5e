package com.kering.cus.marketplace.member.model.param;

import lombok.Data;
import java.io.Serializable;

/**
 * @Auther: guoyichi
 * @Date: 2025/7/22 - 07 - 22 - 上午11:21
 * @Description: com.kering.cus.marketplace.member.model.param
 */
@Data
public class MemberQueryPageParam extends BasePageQuery {
    /**
     * 天猫ouid
     */
    private String ouid;
    /**
     * 昵称
     */
    private String nick;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * CDBid
     */
    private String clientId;
    /**
     * 开始时间
     */
    private String subscribeStartTime;

    /**
     * 结束时间
     */
    private String subscribeEndTime;
}
