package com.kering.cus.lib.persistence.common.config;

import org.apache.commons.io.FileUtils;

import java.io.File;

/**
 * kafka ssl init file
 *
 * @date 2024-08-28
 */
@SuppressWarnings("all")
public class KafkaSslFileInitializer {
    private static final String CONFIG_BASE_PATH = "/configs/";
    private static final String CONFIG_SECRET_BASE_PATH = "/configs/secret/";
    private static final String CONFIG_FILES_BASE_PATH = "/configs/files/";
    private static final String KAFKA_SSL_TRUSTSTORE_BASE64_CERT = "KAFKA_SSL_TRUSTSTORE_CERT_PATH";

    private static final String ROOT_PATH = "./";
    private static final String KAFKA_SSL_KEYSTORE_BASE64_CERT = "KAFKA_SSL_KEYSTORE_CERT_PATH";
    private static final String KAFKA_SSL_TRUSTSTORE_LOCATION_FILE = ROOT_PATH + "KAFKA_SSL_TRUSTSTORE_CERT.jks";
    private static final String KAFKA_SSL_KEYSTORE_LOCATION_FILE = ROOT_PATH + "KAFKA_SSL_KEYSTORE_CERT.jks";
    private static final String KAFKA_SSL_TRUSTSTORE_LOCATION = "KAFKA_SSL_TRUSTSTORE_LOCATION";
    private static final String KAFKA_SSL_KEYSTORE_LOCATION = "KAFKA_SSL_KEYSTORE_LOCATION";


    public static void initKafkaSslFile() {
        //no file created
        final File jksTssFile = FileUtils.getFile(KAFKA_SSL_TRUSTSTORE_LOCATION_FILE);
        if (!jksTssFile.exists()) {
            Base64FileUtil.readValutConfigBase64IfPresent(System.getenv(KAFKA_SSL_TRUSTSTORE_BASE64_CERT), KAFKA_SSL_TRUSTSTORE_LOCATION_FILE);
        }

        //no file created
        final File jksKeysFile = FileUtils.getFile(KAFKA_SSL_KEYSTORE_LOCATION_FILE);
        if (!jksKeysFile.exists()) {
            Base64FileUtil.readValutConfigBase64IfPresent(System.getenv(KAFKA_SSL_KEYSTORE_BASE64_CERT), KAFKA_SSL_KEYSTORE_LOCATION_FILE);
        }

    }

}