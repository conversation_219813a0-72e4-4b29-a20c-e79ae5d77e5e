package com.kering.cus.lib.persistence.common.config;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileAlreadyExistsException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2024/8/30 13:24
 * @version 1.0
 */
@Slf4j
@SuppressWarnings("all")
@SuppressFBWarnings(value = "HARD_CODE_PASSWORD", justification = "This is not a hardcoded password, it's a key name")
public class Base64FileUtil {
    /**
     * 读valut配置base64如果有否则就读环境变量，写入到原始的jks文件
     *
     * @param configB64FilePath config base64 file path
     * @param jksFilePath jks file
     *
     *
     */
    public static void readValutConfigBase64IfPresent(String configB64FilePath, String jksFilePath) {
        final File fileB64 = FileUtils.getFile(configB64FilePath);
        if (fileB64.exists()) {
            try {
                final byte[] decode = Base64.getDecoder().decode(FileUtils.readFileToByteArray(fileB64));
                writeToFile(jksFilePath, decode);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private static void writeToFile(String filePath, byte[] decodedBytes) {
        try {
            FileUtils.writeByteArrayToFile(FileUtils.getFile(filePath), decodedBytes);
        } catch (IOException e) {
            log.error("Failed to write to ssl file.", e);
        }
    }


    public static void readAndWrite2Base64File(String jdsFilePath, String writeFilePath) throws FileAlreadyExistsException {
        final File jksFile = FileUtils.getFile(jdsFilePath);
        if (!jksFile.exists()) {
            return;
        }
        try {
            byte[] bytes = FileUtils.readFileToByteArray(jksFile);
            final byte[] afterEncodingBytes = Base64.getEncoder().encode(bytes);
            writeToFile(writeFilePath, afterEncodingBytes);
        } catch (IOException e) {
            log.error("Failed to read ssl file.", e);
        }
    }


}
